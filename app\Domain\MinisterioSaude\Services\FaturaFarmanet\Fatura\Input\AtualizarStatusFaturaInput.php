<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;

class AtualizarStatusFaturaInput
{
    public string $protocoloIdGsnet;
    public string $nrDocumento;
    public int $idOrigem;
    public ?string $justificativa;


    public function __construct(
        string $protocoloIdGsnet,
        string $nrDocumento,
        int $idOrigem,
        ?string $justificativa
    ) {
        $this->protocoloIdGsnet = $protocoloIdGsnet;
        $this->nrDocumento = $nrDocumento;
        $this->idOrigem = $idOrigem;
        $this->justificativa = $justificativa;
    }

    public static function fromArray(array $data): AtualizarStatusFaturaInput
    {
        return new self(
            $data['protocolo_id_gsnet'],
            $data['nr_documento'],
            $data['id_origem'],
            $data['justificativa'] ?? null,
        );
    }

    public function toArray(): array
    {
        return [
            'protocolo_id_gsnet' => $this->protocoloIdGsnet,
            'nr_documento' => $this->nrDocumento,
            'id_origem' => $this->idOrigem,
            'justificativa' => $this->justificativa
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'protocoloIdGsnet' => $this->protocoloIdGsnet,
            'nrDocumento' => $this->nrDocumento,
            'idOrigem' => $this->idOrigem,
            'justificativa' => $this->justificativa,
            'AccessToken' => $accessToken,
        ];
    }
}
