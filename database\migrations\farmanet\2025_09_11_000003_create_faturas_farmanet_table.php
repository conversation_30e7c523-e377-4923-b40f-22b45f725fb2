<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasFarmanetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_farmanet', function (Blueprint $table) {
            $table->id();
            
            // Identificadores principais
            $table->string('numero_fatura', 50)->unique()->comment('Número da fatura');
            $table->string('id_gestor', 22)->index()->comment('ID do gestor');
            $table->integer('codigo_programa')->nullable()->comment('Código do programa (5,24,25,28)');
            
            // Status da fatura
            $table->tinyInteger('status_fatura')->default(1)->comment('1-Pendente, 2-<PERSON><PERSON>, 3-Pa<PERSON>, 4-Cancelada');
            
            // Datas importantes
            $table->datetime('data_emissao')->nullable()->comment('Data de emissão da fatura');
            $table->datetime('data_vencimento')->nullable()->comment('Data de vencimento');
            $table->datetime('data_pagamento')->nullable()->comment('Data de pagamento');
            $table->datetime('data_ultima_atualizacao')->nullable()->comment('Última atualização');
            
            // Valores
            $table->decimal('valor_total', 15, 2)->nullable()->comment('Valor total da fatura');
            $table->decimal('valor_pago', 15, 2)->nullable()->comment('Valor pago');
            
            // Observações e dados originais
            $table->text('observacoes')->nullable()->comment('Observações gerais');
            $table->longText('dados_originais')->nullable()->comment('JSON com dados originais da API');
            
            // Controle interno
            $table->timestamps();
            
            // Índices
            $table->index(['id_gestor', 'codigo_programa']);
            $table->index(['status_fatura', 'data_emissao']);
            $table->index('data_emissao');
            $table->index('data_vencimento');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_farmanet');
    }
}
