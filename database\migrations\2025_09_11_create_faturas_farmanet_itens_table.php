<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasFarmanetItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_farmanet_itens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fatura_id')->comment('FK para faturas_farmanet');
            $table->integer('codigo_item')->comment('Código item material do sistema GSNET');
            $table->decimal('quantidade', 16, 3)->comment('Quantidade do item');
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Foreign key
            $table->foreign('fatura_id')->references('id')->on('faturas_farmanet')->onDelete('cascade');
            
            // Indexes
            $table->index(['fatura_id']);
            $table->index(['codigo_item']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_farmanet_itens');
    }
}
