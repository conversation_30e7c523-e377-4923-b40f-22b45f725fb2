<?php

namespace Domain\MinisterioSaude\Exceptions;

use Exception;
use Throwable;

class DuplicateRecordException extends Exception
{
    private array $duplicatedFields;

    public function __construct(string $message, array $duplicatedFields = [], int $code = 422, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->duplicatedFields = $duplicatedFields;
    }

    public function getDuplicatedFields(): array
    {
        return $this->duplicatedFields;
    }

    public function hasDuplicatedFields(): bool
    {
        return !empty($this->duplicatedFields);
    }
}
