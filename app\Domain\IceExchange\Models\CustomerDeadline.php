<?php

namespace Domain\IceExchange\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerDeadline extends Model
{
    protected $table = 'ice_change_deadlines';
    protected $connection = 'customers_service';

    protected $fillable = [
        'customer_id',
        'deadline_hours',
    ];

    protected $casts = [
        'deadline_hours' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relacionamento com o cliente
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\Domain\Customers\Models\Customer::class, 'customer_id');
    }
} 