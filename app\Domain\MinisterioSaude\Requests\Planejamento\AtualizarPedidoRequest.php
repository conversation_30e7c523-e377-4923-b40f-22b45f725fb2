<?php

namespace Domain\MinisterioSaude\Requests\Planejamento;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class AtualizarPedidoRequest extends ApiFormRequest
{
    public function rules(): array
    {
        return [
            'id_programa_saude' => 'required|integer',
            'codigo_pedido' => 'required|integer',
            'protocolo_operador' => 'required|string|max:50'
        ];
    }

    public function messages(): array
    {
        return [
            'id_programa_saude.required' => 'O ID do programa de saúde é obrigatório',
            'id_programa_saude.integer' => 'O ID do programa de saúde deve ser um número inteiro',
            'codigo_pedido.required' => 'O código do pedido é obrigatório',
            'codigo_pedido.integer' => 'O código do pedido deve ser um número inteiro',
            'protocolo_operador.required' => 'O protocolo do operador é obrigatório',
            'protocolo_operador.string' => 'O protocolo do operador deve ser uma string',
            'protocolo_operador.max' => 'O protocolo do operador deve ter no máximo 50 caracteres'
        ];
    }

    public function attributes(): array
    {
        return [
            'id_programa_saude' => 'ID do Programa de Saúde',
            'codigo_pedido' => 'Código do Pedido',
            'protocolo_operador' => 'Protocolo do Operador'
        ];
    }
}
