{"_type": "export", "__export_format": 4, "__export_date": "2025-09-11T15:00:00.000Z", "__export_source": "insomnia.desktop.app:v8.0.0", "resources": [{"_id": "wrk_ministerio_saude", "parentId": null, "modified": 1725984000000, "created": 1725984000000, "name": "🏥 Ministério da Saúde SP - Sistema Completo", "description": "Sistema completo com 10 APIs do Ministério da Saúde SP - Gestão de Operadores, Faturas, Farmanet e Itens GSNET", "_type": "workspace"}, {"_id": "env_base", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "🔧 Desenvolvimento", "data": {"base_url": "http://localhost:8000", "jwt_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0L2FwaS9hdXRoL2xvZ2luIiwiaWF0IjoxNzM2MjczNjQzLCJleHAiOjE3MzYyNzczNDMsIm5iZiI6MTczNjI3MzY0MywianRpIjoiSk96Z3B3ZUJYSXowS0tBOCIsInN1YiI6IjEiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.fQ8OmdBIwCQaC72Fn5Xvu1Bk0n4-L7mf8y5yIiFEGUY", "id_gestor": "12345678901234567890", "ano_referencia": "2024", "mes_referencia": "9", "codigo_programa": "5", "access_token": "MS_ACCESS_TOKEN_HERE"}, "dataPropertyOrder": {"&": ["base_url", "jwt_token", "id_gestor", "ano_referencia", "mes_referencia", "codigo_programa", "access_token"]}, "color": "#7d69cb", "isPrivate": false, "metaSortKey": 1000000000, "_type": "environment"}, {"_id": "env_production", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "🚀 Produção", "data": {"base_url": "https://api.ibl.com.br", "jwt_token": "PRODUCTION_JWT_TOKEN_HERE", "id_gestor": "REAL_ID_GESTOR_HERE", "ano_referencia": "2025", "mes_referencia": "9", "codigo_programa": "5", "access_token": "PRODUCTION_MS_TOKEN_HERE"}, "dataPropertyOrder": {"&": ["base_url", "jwt_token", "id_gestor", "ano_referencia", "mes_referencia", "codigo_programa", "access_token"]}, "color": "#d50000", "isPrivate": false, "metaSortKey": 2000000000, "_type": "environment"}, {"_id": "fld_apis_basicas", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "📋 APIs Básicas (1.1 - 1.4)", "description": "APIs essenciais de operadores, status e itens GSNET", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1000000000, "_type": "request_group"}, {"_id": "fld_apis_faturas", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "💰 APIs de Faturas (2.1 - 2.2)", "description": "APIs de gerenciamento de faturas GSNET", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -900000000, "_type": "request_group"}, {"_id": "fld_apis_farmanet", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "💊 APIs Farmanet (3.1 - 3.4)", "description": "APIs de gerenciamento farmacêutico Farmanet", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -800000000, "_type": "request_group"}, {"_id": "req_listar_operadores", "parentId": "fld_apis_basicas", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/operadores", "name": "🔵 1.1 - Listar Operadores", "description": "API para listar todos os operadores cadastrados no sistema", "method": "GET", "body": {}, "parameters": [{"name": "page", "value": "1", "description": "Página (opcional)", "disabled": true}, {"name": "per_page", "value": "20", "description": "Itens por página (opcional)", "disabled": true}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -1000000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_consultar_status", "parentId": "fld_apis_basicas", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/status", "name": "🔍 1.2 - Consultar Status", "description": "API para consultar status de operações específicas", "method": "GET", "body": {}, "parameters": [{"name": "id_origem", "value": "12345", "description": "ID de origem da operação (opcional)"}, {"name": "nome_status", "value": "ATIVO", "description": "Nome do status (opcional)"}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -900000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_consultar_itens", "parentId": "fld_apis_basicas", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/itens/consultar", "name": "🔍 1.4 - Consultar Itens GSNET", "description": "API para consultar itens do sistema GSNET por código de material", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"ListaItens\": [\n      {\n        \"CodigoMaterial\": 1560\n      },\n      {\n        \"CodigoMaterial\": 785\n      },\n      {\n        \"CodigoMaterial\": 3500\n      },\n      {\n        \"CodigoMaterial\": 2560\n      }\n    ]\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -850000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_consultar_faturas", "parentId": "fld_apis_faturas", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas", "name": "💰 2.1 - Consul<PERSON> Faturas", "description": "API para consultar faturas do sistema GSNET", "method": "GET", "body": {}, "parameters": [{"name": "id_gestor", "value": "{{ _.id_gestor }}", "description": "ID do gestor (obrigatório)"}, {"name": "data_inicio", "value": "2024-01-01", "description": "Data de início (formato: YYYY-MM-DD)", "disabled": true}, {"name": "data_fim", "value": "2024-12-31", "description": "Data de fim (formato: YYYY-MM-DD)", "disabled": true}, {"name": "local_origem_id", "value": "1", "description": "ID do local de origem", "disabled": true}, {"name": "status", "value": "ATIVO", "description": "Status da fatura", "disabled": true}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -800000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_detalhes_fatura", "parentId": "fld_apis_faturas", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas/FAT123456789/detalhes", "name": "📄 2.2 - Consultar <PERSON><PERSON><PERSON>", "description": "API para consultar detalhes específicos de uma fatura", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -700000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_consultar_pedidos_farmanet", "parentId": "fld_apis_farmanet", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/pedidos-farmanet", "name": "💊 3.1 - Consultar Pedidos Farmanet (DADOS REAIS)", "description": "API para consultar pedidos farmacêuticos - CONECTA COM MINISTÉRIO REAL", "method": "GET", "body": {}, "parameters": [{"name": "id_gestor", "value": "{{ _.id_gestor }}", "description": "ID do gestor (obrigatório)"}, {"name": "access_token", "value": "{{ _.access_token }}", "description": "Token de acesso ao Ministério"}, {"name": "data_inicio", "value": "2024-01-01", "description": "Data de início (opcional)", "disabled": true}, {"name": "data_fim", "value": "2024-12-31", "description": "Data de fim (opcional)", "disabled": true}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -600000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_atualizar_pedidos_farmanet", "parentId": "fld_apis_farmanet", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/pedidos-farmanet/atualizar", "name": "🔄 3.2 - Atualizar Pedidos Farmanet", "description": "API para atualizar status de pedidos farmacêuticos", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"id\": \"123\",\n  \"status\": \"ativo\",\n  \"observacoes\": \"Pedido atualizado via API\",\n  \"data_atualizacao\": \"2024-09-11T10:30:00\",\n  \"access_token\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -500000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_obter_itens_pedido", "parentId": "fld_apis_farmanet", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/pedidos-farmanet/123456/itens", "name": "📦 3.3 - Obter Itens do Pedido", "description": "API para consultar itens específicos de um pedido Farmanet", "method": "GET", "body": {}, "parameters": [{"name": "access_token", "value": "{{ _.access_token }}", "description": "Token de acesso ao Ministério"}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -400000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_consultar_faturas_farmanet", "parentId": "fld_apis_farmanet", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas-farmanet/consultar", "name": "💰 3.4 - Consultar Faturas Farmanet (SIMULADO)", "description": "API para consultar faturas farmacêuticas - DADOS SIMULADOS", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"id_gestor\": \"{{ _.id_gestor }}\",\n  \"ano_referencia\": \"{{ _.ano_referencia }}\",\n  \"mes_referencia\": \"{{ _.mes_referencia }}\",\n  \"codigo_programa\": \"{{ _.codigo_programa }}\",\n  \"access_token\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "Token JWT de autenticação"}], "authentication": {}, "metaSortKey": -300000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_test_connection", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "url": "{{ _.base_url }}/api/ministerio-saude-test", "name": "🔗 Teste de Conexão", "description": "Endpoint para testar se o módulo está funcionando", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -100000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "jar_cookies", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}, {"_id": "spc_space", "parentId": "wrk_ministerio_saude", "modified": 1725984000000, "created": 1725984000000, "fileName": "Ministério da Saúde SP - Sistema Completo", "contents": "", "contentType": "yaml", "_type": "api_spec"}]}