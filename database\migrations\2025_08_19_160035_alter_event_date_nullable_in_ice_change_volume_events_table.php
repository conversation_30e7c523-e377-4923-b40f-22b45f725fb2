<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterEventDateNullableInIceChangeVolumeEventsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ice_change_volume_events', function (Blueprint $table) {
            $table->datetime('event_date')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ice_change_volume_events', function (Blueprint $table) {
            $table->datetime('event_date')->nullable(false)->change();
        });
    }
}
