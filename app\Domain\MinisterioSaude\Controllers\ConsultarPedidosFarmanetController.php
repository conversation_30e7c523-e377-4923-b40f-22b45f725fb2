<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Domain\MinisterioSaude\Requests\ConsultarPedidosFarmanetRequest;
use Domain\MinisterioSaude\Services\ConsultarPedidosFarmanetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ConsultarPedidosFarmanetController extends Controller
{
    protected $consultarPedidosService;

    public function __construct(ConsultarPedidosFarmanetService $consultarPedidosService)
    {
        $this->consultarPedidosService = $consultarPedidosService;
    }

    /**
     * Consulta pedidos do Farmanet via API do Ministério da Saúde
     *
     * @param ConsultarPedidosFarmanetRequest $request
     * @return JsonResponse
     */
    public function consultarPedidos(ConsultarPedidosFarmanetRequest $request): JsonResponse
    {
        try {
            Log::info('ConsultarPedidosFarmanetController - Iniciando consulta de pedidos', [
                'request_data' => $request->validated()
            ]);

            $result = $this->consultarPedidosService->consultarPedidos($request->validated());

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pedidos consultados com sucesso',
                    'data' => $result['data'],
                    'total_pedidos' => $result['total_pedidos'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao consultar pedidos',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('ConsultarPedidosFarmanetController - Erro não tratado', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Busca pedidos armazenados localmente
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function buscarPedidosLocais(Request $request): JsonResponse
    {
        try {
            Log::info('ConsultarPedidosFarmanetController - Buscando pedidos locais', [
                'filters' => $request->all()
            ]);

            $result = $this->consultarPedidosService->buscarPedidosLocais($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Pedidos locais consultados com sucesso',
                'pedidos' => $result['pedidos'],
                'total' => $result['total'],
                'timestamp' => $result['timestamp']
            ], 200);

        } catch (\Exception $e) {
            Log::error('ConsultarPedidosFarmanetController - Erro ao buscar pedidos locais', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Retorna informações sobre a API
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'api' => 'API 3.1 - Consultar Pedidos Farmanet',
            'description' => 'Consulta pedidos farmacêuticos do programa Farmanet junto ao Ministério da Saúde',
            'version' => '1.0.0',
            'endpoints' => [
                'POST /ministerio-saude/pedidos-farmanet/consultar' => 'Consultar pedidos na API do MS',
                'GET /ministerio-saude/pedidos-farmanet/locais' => 'Buscar pedidos armazenados localmente',
                'GET /ministerio-saude/pedidos-farmanet/info' => 'Informações sobre a API'
            ],
            'required_parameters' => [
                'id_gestor' => 'string (max 22)',
                'ano_referencia' => 'integer (YYYY)',
                'access_token' => 'string (max 40)'
            ],
            'optional_parameters' => [
                'codigo_programa' => 'integer (5,24,25,28)',
                'mes_referencia' => 'integer (1-12)',
                'ano_periodo_referencia' => 'integer (YYYY)'
            ],
            'programs' => [
                5 => 'Diabetes',
                24 => 'Dose Certa',
                25 => 'Saúde da Mulher',
                28 => 'Arboviroses'
            ]
        ], 200);
    }
}
