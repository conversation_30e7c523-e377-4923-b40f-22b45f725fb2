<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\PrecoMedioItem;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ObterPrecoMedioItensService
{
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.farmanet.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
    }

    /**
     * Obter preço médio de itens do Ministério da Saúde
     *
     * @param array $params
     * @return array
     */
    public function obterPrecoMedio(array $params): array
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.obter_preco_medio');
            $url = $this->baseUrl . $endpoint;
            
            Log::info('ObterPrecoMedioItensService - Iniciando consulta', [
                'url' => $url,
                'params' => $params
            ]);

            $queryParams = [
                'codigo_programa' => $params['codigo_programa'],
                'ano_referencia' => $params['ano_referencia']
            ];

            // Adicionar parâmetros opcionais
            if (isset($params['mes_referencia'])) {
                $queryParams['mes_referencia'] = $params['mes_referencia'];
            }

            if (isset($params['codigo_medicamento'])) {
                $queryParams['codigo_medicamento'] = $params['codigo_medicamento'];
            }

            if (isset($params['codigo_farmaco'])) {
                $queryParams['codigo_farmaco'] = $params['codigo_farmaco'];
            }

            if (isset($params['estado_origem'])) {
                $queryParams['estado_origem'] = $params['estado_origem'];
            }

            $response = $this->client->get($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $params['access_token'],
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'query' => $queryParams
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            Log::info('ObterPrecoMedioItensService - Resposta recebida', [
                'status_code' => $response->getStatusCode(),
                'total_itens' => count($data['itens'] ?? [])
            ]);

            // Processar e armazenar os preços médios localmente
            if (isset($data['itens']) && is_array($data['itens'])) {
                $this->processarEArmazenarPrecos($data['itens'], $params);
            }

            return [
                'success' => true,
                'data' => $data,
                'total_itens' => count($data['itens'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('ObterPrecoMedioItensService - Erro na requisição', [
                'message' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'params' => $params
            ]);

            // Não usar dados simulados - falhar se API real não funcionar
            return [
                'success' => false,
                'error' => $errorMessage,
                'timestamp' => Carbon::now()->toISOString()
            ];
            
        } catch (\Exception $e) {
            $errorMessage = 'Erro interno: ' . $e->getMessage();
            
            Log::error('ObterPrecoMedioItensService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $params
            ]);

            // Não usar dados simulados - falhar se API real não funcionar
            return [
                'success' => false,
                'error' => $errorMessage,
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Processa e armazena preços médios recebidos da API
     *
     * @param array $itens
     * @param array $params
     * @return void
     */
    private function processarEArmazenarPrecos(array $itens, array $params): void
    {
        foreach ($itens as $itemData) {
            try {
                // Buscar ou criar o registro de preço médio
                $precoMedio = PrecoMedioItem::updateOrCreate(
                    [
                        'codigo_programa' => $params['codigo_programa'],
                        'codigo_medicamento' => $itemData['codigo_medicamento'] ?? null,
                        'codigo_farmaco' => $itemData['codigo_farmaco'] ?? null,
                        'ano_referencia' => $params['ano_referencia'],
                        'mes_referencia' => $params['mes_referencia'] ?? null,
                        'estado_origem' => $params['estado_origem'] ?? null,
                    ],
                    [
                        'nome_medicamento' => $itemData['nome_medicamento'] ?? null,
                        'nome_farmaco' => $itemData['nome_farmaco'] ?? null,
                        'unidade_medida' => $itemData['unidade_medida'] ?? null,
                        'preco_medio_unitario' => $itemData['preco_medio'] ?? null,
                        'preco_minimo' => $itemData['preco_minimo'] ?? null,
                        'preco_maximo' => $itemData['preco_maximo'] ?? null,
                        'quantidade_amostras' => $itemData['quantidade_amostras'] ?? null,
                        'desvio_padrao' => $itemData['desvio_padrao'] ?? null,
                        'data_calculo' => isset($itemData['data_calculo']) ? Carbon::parse($itemData['data_calculo']) : null,
                        'observacoes' => $itemData['observacoes'] ?? null,
                        'data_ultima_atualizacao' => Carbon::now(),
                        'dados_originais' => json_encode($itemData)
                    ]
                );

                Log::info('Preço médio processado', [
                    'preco_medio_id' => $precoMedio->id,
                    'codigo_medicamento' => $precoMedio->codigo_medicamento,
                    'preco_medio' => $precoMedio->preco_medio_unitario
                ]);

            } catch (\Exception $e) {
                Log::error('Erro ao processar preço médio', [
                    'item_data' => $itemData,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Busca preços médios locais com filtros
     *
     * @param array $filters
     * @return array
     */
    public function buscarPrecosLocais(array $filters = []): array
    {
        $query = PrecoMedioItem::query();

        if (isset($filters['codigo_programa'])) {
            $query->where('codigo_programa', $filters['codigo_programa']);
        }

        if (isset($filters['ano_referencia'])) {
            $query->where('ano_referencia', $filters['ano_referencia']);
        }

        if (isset($filters['mes_referencia'])) {
            $query->where('mes_referencia', $filters['mes_referencia']);
        }

        if (isset($filters['codigo_medicamento'])) {
            $query->where('codigo_medicamento', $filters['codigo_medicamento']);
        }

        if (isset($filters['codigo_farmaco'])) {
            $query->where('codigo_farmaco', $filters['codigo_farmaco']);
        }

        if (isset($filters['estado_origem'])) {
            $query->where('estado_origem', $filters['estado_origem']);
        }

        if (isset($filters['nome_medicamento'])) {
            $query->where('nome_medicamento', 'like', '%' . $filters['nome_medicamento'] . '%');
        }

        $precos = $query->orderBy('preco_medio_unitario', 'asc')->get();

        return [
            'success' => true,
            'precos' => $precos->toArray(),
            'total' => $precos->count(),
            'timestamp' => Carbon::now()->toISOString()
        ];
    }

    /**
     * Obter estatísticas de preços por programa
     *
     * @param int $codigoPrograma
     * @param int $anoReferencia
     * @param int|null $mesReferencia
     * @return array
     */
    public function obterEstatisticasPrograma(int $codigoPrograma, int $anoReferencia, ?int $mesReferencia = null): array
    {
        try {
            $query = PrecoMedioItem::where('codigo_programa', $codigoPrograma)
                                   ->where('ano_referencia', $anoReferencia);

            if ($mesReferencia) {
                $query->where('mes_referencia', $mesReferencia);
            }

            $precos = $query->get();

            if ($precos->isEmpty()) {
                return [
                    'success' => true,
                    'estatisticas' => [],
                    'message' => 'Nenhum preço encontrado para os parâmetros informados'
                ];
            }

            $estatisticas = [
                'total_itens' => $precos->count(),
                'preco_medio_geral' => $precos->avg('preco_medio_unitario'),
                'preco_minimo_geral' => $precos->min('preco_medio_unitario'),
                'preco_maximo_geral' => $precos->max('preco_medio_unitario'),
                'total_amostras' => $precos->sum('quantidade_amostras'),
                'medicamentos_unicos' => $precos->unique('codigo_medicamento')->count(),
                'farmacos_unicos' => $precos->unique('codigo_farmaco')->count(),
                'por_estado' => $precos->groupBy('estado_origem')->map(function ($grupo) {
                    return [
                        'total_itens' => $grupo->count(),
                        'preco_medio' => $grupo->avg('preco_medio_unitario'),
                        'preco_minimo' => $grupo->min('preco_medio_unitario'),
                        'preco_maximo' => $grupo->max('preco_medio_unitario')
                    ];
                })
            ];

            return [
                'success' => true,
                'estatisticas' => $estatisticas,
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Erro ao obter estatísticas do programa', [
                'codigo_programa' => $codigoPrograma,
                'ano_referencia' => $anoReferencia,
                'mes_referencia' => $mesReferencia,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Comparar preços entre períodos
     *
     * @param array $params
     * @return array
     */
    public function compararPrecosPeriodos(array $params): array
    {
        try {
            $precoAtual = PrecoMedioItem::where('codigo_programa', $params['codigo_programa'])
                                       ->where('codigo_medicamento', $params['codigo_medicamento'])
                                       ->where('ano_referencia', $params['ano_atual'])
                                       ->where('mes_referencia', $params['mes_atual'] ?? null)
                                       ->first();

            $precoAnterior = PrecoMedioItem::where('codigo_programa', $params['codigo_programa'])
                                          ->where('codigo_medicamento', $params['codigo_medicamento'])
                                          ->where('ano_referencia', $params['ano_anterior'])
                                          ->where('mes_referencia', $params['mes_anterior'] ?? null)
                                          ->first();

            $comparacao = [
                'medicamento' => $params['codigo_medicamento'],
                'preco_atual' => $precoAtual ? $precoAtual->preco_medio_unitario : null,
                'preco_anterior' => $precoAnterior ? $precoAnterior->preco_medio_unitario : null,
                'variacao_absoluta' => null,
                'variacao_percentual' => null,
                'tendencia' => 'indefinida'
            ];

            if ($precoAtual && $precoAnterior) {
                $comparacao['variacao_absoluta'] = $precoAtual->preco_medio_unitario - $precoAnterior->preco_medio_unitario;
                $comparacao['variacao_percentual'] = (($precoAtual->preco_medio_unitario - $precoAnterior->preco_medio_unitario) / $precoAnterior->preco_medio_unitario) * 100;
                
                if ($comparacao['variacao_absoluta'] > 0) {
                    $comparacao['tendencia'] = 'alta';
                } elseif ($comparacao['variacao_absoluta'] < 0) {
                    $comparacao['tendencia'] = 'baixa';
                } else {
                    $comparacao['tendencia'] = 'estável';
                }
            }

            return [
                'success' => true,
                'comparacao' => $comparacao,
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (\Exception $e) {
            Log::error('Erro ao comparar preços entre períodos', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Lista os programas disponíveis
     *
     * @return array
     */
    public function obterProgramasDisponiveis(): array
    {
        return [
            5 => 'Diabetes',
            24 => 'Dose Certa',
            25 => 'Saúde da Mulher',
            28 => 'Arboviroses'
        ];
    }
}
