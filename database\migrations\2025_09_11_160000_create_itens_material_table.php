<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('itens_material', function (Blueprint $table) {
            $table->id();
            
            // Campos principais do item
            $table->bigInteger('codigo_material')->unique()->comment('Código item material do sistema GSNET');
            $table->string('nome_material', 120)->nullable()->comment('Nome/Descrição do item');
            $table->string('nome_descricao_tecnica', 120)->nullable()->comment('Nome/Descrição técnica do item');
            $table->string('nome_unidade_medida', 120)->nullable()->comment('Nome da unidade de medida do item');
            $table->char('st_registro', 2)->nullable()->comment('Status do registro na base');
            $table->string('codigo_siafisico', 120)->nullable()->comment('Código SIAFISICO do item material do sistema GSNET');
            
            // Campos de controle
            $table->datetime('data_ultima_consulta')->nullable()->comment('Data da última consulta ao Ministério');
            $table->string('origem', 50)->default('ministerio_saude_sp')->comment('Origem dos dados');
            
            // Timestamps
            $table->timestamps();
            
            // Índices
            $table->index('codigo_material');
            $table->index('st_registro');
            $table->index('nome_unidade_medida');
            $table->index(['codigo_material', 'st_registro']);
            $table->index('data_ultima_consulta');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('itens_material');
    }
};
