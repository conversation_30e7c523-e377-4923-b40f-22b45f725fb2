<?php

namespace Domain\MinisterioSaude\Repositories\Common;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

abstract class BaseAbastractRepository
{
    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    abstract public function list(array $filtros): Collection;

    public function get(int $id): Model
    {
        return $this->model->where('id', $id)->firstOrFail();
    }

    public function getOneWhere(callable $where): ?Model
    {
        return $this->model->where($where)->first();
    }

    public function getWhere(callable $where): Collection
    {
        return $this->model->where($where)->get();
    }

    public function findBy(array $conditions): Model
    {
        $query = $this->model->newQuery();

        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query->whereIn($field, $value);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->firstOrFail();
    }

    public function store(array $data): Collection
    {
        return new Collection($this->model->create($data));
    }

    public function storeArray(array $data): Collection
    {
        return new Collection($this->model->create($data));
    }

    public function storeSingleData(array $data): Collection
    {
        return new Collection($this->model->firstOrCreate($data));
    }

    public function updateOrStore(array $attributes, ?array $values): Collection
    {
        return new Collection($this->model->updateOrCreate($attributes, $values));
    }

    public function update(array $dados, int $id): bool
    {
        return $this->model->findOrFail($id)->update($dados);
    }
    public function updateArray(array $data, int $id): bool
    {
        return $this->model->findOrFail($id)->update($data);
    }

    public function destroy(int $id): bool
    {
        return $this->model->findOrFail($id)->delete();
    }

    public function searchByCustomField(string $field, string $value): Collection
    {
        return $this->model->where($field, $value)->get();
    }

}
