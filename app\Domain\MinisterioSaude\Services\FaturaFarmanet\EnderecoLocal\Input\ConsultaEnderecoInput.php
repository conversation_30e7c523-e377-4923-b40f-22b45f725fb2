<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input;

class ConsultaEnderecoInput
{
    public string $idGestor;
    public string $idLocal;
    private array $errors = [];

    public function __construct(string $idGestor, string $idLocal)
    {
        $this->idGestor = $idGestor;
        $this->idLocal  = $idLocal;
    }

    public static function fromArray(array $data): ConsultaEnderecoInput
    {
        return new self(
            $data['IdGestor'],
            $data['IdLocal']
        );
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'IdGestor' => $this->idGestor,
            'IdLocal' => $this->idLocal,
            'AccessToken' => $accessToken
        ];
    }

    public function toArray(): array
    {
        return [
            'IdGestor' => $this->idGestor,
            'IdLocal' => $this->idLocal
        ];
    }

    public function validate(): void
    {
        $errors = [];

        if (empty($this->idGestor)) {
            $errors[] = 'IdGestor não informado';
        } elseif (strlen($this->idGestor) > 4) {
            $errors[] = 'IdGestor deve ter no máximo 4 caracteres';
        }

        if (empty($this->idLocal)) {
            $errors[] = 'IdLocal não informado';
        } elseif (strlen($this->idLocal) > 5) {
            $errors[] = 'IdLocal deve ter no máximo 5 caracteres';
        }

        $this->errors = $errors;
    }

    public function isValid(): bool
    {
        $this->validate();
        return empty($this->errors);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }
}
