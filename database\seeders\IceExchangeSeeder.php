<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Domain\IceExchange\Models\Customer;
use Domain\IceExchange\Models\CustomerDeadline;
use Domain\IceExchange\Models\IceItem;
use Domain\IceExchange\Models\IceWarehouse;

class IceExchangeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Criar tipos de gelo
        $iceItems = [
            ['name' => 'Gelo em Gel', 'description' => 'Gelo em formato de gel'],
            ['name' => 'Gelo em Cubos', 'description' => 'Gelo em formato de cubos'],
            ['name' => 'Gelo Granulado', 'description' => 'Gelo em formato granulado'],
            ['name' => 'G<PERSON> Especial', 'description' => 'Gelo com características especiais'],
        ];

        foreach ($iceItems as $item) {
            IceItem::create($item);
        }

        // Criar armazéns/câmaras frias
        $warehouses = [
            ['name' => 'Câmara <PERSON>ia 1', 'description' => 'Câmara fria principal'],
            ['name' => 'Freezer 2', 'description' => 'Freezer secundário'],
            ['name' => 'Câmara Fria 3', 'description' => 'Câmara fria de emergência'],
        ];

        foreach ($warehouses as $warehouse) {
            IceWarehouse::create($warehouse);
        }

        // Criar clientes com prazos
        $customers = [
            [
                'name' => 'Cliente A',
                'document' => '12.345.678/0001-90',
                'email' => '<EMAIL>',
                'phone' => '(11) 99999-9999',
                'address' => 'Rua A, 123 - São Paulo/SP',
                'deadlines' => [48, 72, 96]
            ],
            [
                'name' => 'Cliente B',
                'document' => '98.765.432/0001-10',
                'email' => '<EMAIL>',
                'phone' => '(11) 88888-8888',
                'address' => 'Rua B, 456 - Rio de Janeiro/RJ',
                'deadlines' => [24, 48, 72, 120]
            ],
            [
                'name' => 'Cliente C',
                'document' => '11.222.333/0001-44',
                'email' => '<EMAIL>',
                'phone' => '(11) 77777-7777',
                'address' => 'Rua C, 789 - Belo Horizonte/MG',
                'deadlines' => [36, 60, 84]
            ],
        ];

        foreach ($customers as $customerData) {
            $deadlines = $customerData['deadlines'];
            unset($customerData['deadlines']);
            
            $customer = Customer::create($customerData);
            
            // Criar prazos para o cliente
            foreach ($deadlines as $deadlineHours) {
                CustomerDeadline::create([
                    'customer_id' => $customer->id,
                    'deadline_hours' => $deadlineHours,
                ]);
            }
        }
    }
} 