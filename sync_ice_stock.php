<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Domain\IceExchange\Models\VolumeEvent;
use Domain\IceExchange\Models\IceStock;

echo "=== Sincronização da tabela ice_change_stock ===\n";

// Buscar todos os volumes com eventos de câmara
$volumeIds = VolumeEvent::whereIn('event_type', ['cold_chamber_entry', 'cold_chamber_exit'])
    ->distinct()
    ->pluck('notfis_volume_id');

echo "Encontrados " . $volumeIds->count() . " volumes com eventos de câmara\n";

foreach ($volumeIds as $volumeId) {
    echo "Sincronizando volume ID: {$volumeId}\n";
    
    // Buscar todos os eventos de câmara para este volume
    $events = VolumeEvent::where('notfis_volume_id', $volumeId)
        ->whereIn('event_type', ['cold_chamber_entry', 'cold_chamber_exit'])
        ->orderBy('created_at', 'asc')
        ->get();

    // Limpar registros existentes
    IceStock::where('notfis_volume_id', $volumeId)->delete();

    // Recriar registros baseado nos eventos
    foreach ($events as $event) {
        $status = $event->event_type === 'cold_chamber_entry' ? 'in' : 'out';
        
        IceStock::create([
            'warehouse_id' => $event->warehouse_id,
            'notfis_volume_id' => $volumeId,
            'status' => $status,
            'created_at' => $event->created_at,
            'updated_at' => $event->updated_at,
        ]);
        
        echo "  - Criado registro: {$status} no armazém {$event->warehouse_id} em {$event->created_at}\n";
    }
}

echo "\n=== Sincronização concluída! ===\n";
