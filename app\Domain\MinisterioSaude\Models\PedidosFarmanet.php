<?php

namespace Domain\MinisterioSaude\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PedidosFarmanet extends Model
{
    use SoftDeletes;

    protected $connection = 'ministerio_saude_sp';
    protected $table = 'pedidos_farmanet';

    protected $fillable = [
        'id_gestor',
        'dt_emissao',
        'dt_emissao_fim',
        'protocolo',
        'codigo_programa',
        'numero_pedido',
        'codigo_periodicidade',
        'ano_periodo',
        'numero_periodo',
        'id_local',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'codigo_programa' => 'integer',
        'numero_pedido' => 'integer',
        'codigo_periodicidade' => 'integer',
        'ano_periodo' => 'integer',
        'numero_periodo' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Scope para buscar por ID de origem
     */
    public function scopeByIdOrigem($query, $idOrigem)
    {
        return $query->where('id_origem', $idOrigem);
    }

    /**
     * Scope para buscar por nome do status
     */
    public function scopeByNomeStatus($query, $nomeStatus)
    {
        return $query->where('nome_status', $nomeStatus);
    }

    /**
     * Scope para registros ativos
     */
    public function scopeAtivos($query)
    {
        return $query->where('flag_registro', true);
    }
}
