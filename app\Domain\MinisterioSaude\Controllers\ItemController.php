<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Traits\HasLog;
use Illuminate\Http\JsonResponse;
use App\Controllers\Controller;
// Requests
use Domain\MinisterioSaude\Requests\Item\ConsultarItensRequest;
use Domain\MinisterioSaude\Requests\Item\ObterPrecoMedioItensRequest;
// Inputs
use Domain\MinisterioSaude\Services\Item\Input\ObterPrecoMedioItensInput;
use Domain\MinisterioSaude\Services\Item\Input\ConsultarItensInput;
// Services
use Domain\MinisterioSaude\Services\Item\ItemService;

class ItemController extends Controller
{
    use HasLog;

    private ItemService $itemService;

    public function __construct(ItemService $itemService)
    {
        $this->itemService = $itemService;
    }

    /**
     * API 1.4 - Consultar Itens do Sistema GSNET
     *
     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
     *
     * @param ConsultarItensRequest $request
     * @return JsonResponse
     */
    public function consultarItens(ConsultarItensRequest $request): JsonResponse
    {
        try {
            $result = $this->itemService->consultarItens(ConsultarItensInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('ItemController@consultarItens - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 3.4 - Obter Preço Médio de Itens
     *
     * @param ObterPrecoMedioItensRequest $request
     * @return JsonResponse
     */
    public function obterPrecoMedio(ObterPrecoMedioItensRequest $request): JsonResponse
    {
        try {
            $result = $this->itemService->obterPrecoMedio(ObterPrecoMedioItensInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('ItemController@obterPrecoMedio - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }
}
