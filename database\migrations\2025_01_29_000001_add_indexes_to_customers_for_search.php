<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Adicionar índices na tabela customers para otimizar buscas
        Schema::connection('customers_service')->table('customers', function (Blueprint $table) {
            // Índice para busca por nome
            $table->index('name', 'idx_customers_name');
            
            // Índice para busca por CNPJ
            $table->index('cnpj', 'idx_customers_cnpj');
            
            // Índice composto para busca por nome e status ativo (não deletado)
            $table->index(['name', 'deleted_at'], 'idx_customers_name_active');
            
            // Índice composto para busca por CNPJ e status ativo (não deletado)
            $table->index(['cnpj', 'deleted_at'], 'idx_customers_cnpj_active');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('customers_service')->table('customers', function (Blueprint $table) {
            $table->dropIndex('idx_customers_name');
            $table->dropIndex('idx_customers_cnpj');
            $table->dropIndex('idx_customers_name_active');
            $table->dropIndex('idx_customers_cnpj_active');
        });
    }
};