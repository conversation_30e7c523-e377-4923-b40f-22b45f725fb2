<?php

namespace Domain\MinisterioSaude\Services;

use Domain\MinisterioSaude\Models\StatusOperador;
use Domain\MinisterioSaude\DTOs\ConsultarEnderecoDTO;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MinisterioSaudeApiService
{
    private $baseUrl;
    private $accessToken;
    private $systemCode;

    public function __construct()
    {
        $environment = config('app.env') === 'production' ? 'PROD' : 'HOMOLOG';
        
        $this->baseUrl = $environment === 'PROD' 
            ? env('MS_SP_API_BASE_URL_PROD')
            : env('MS_SP_API_BASE_URL_HOMOLOG');
            
        $this->accessToken = env('MS_SP_API_ACCESS_TOKEN');
        $this->systemCode = env('MS_SP_API_SYSTEM_CODE');
    }

    /**
     * Inserir status na API do Ministério da Saúde
     */
    public function inserirStatusFatura($idOrigem, $nomeStatus, $descricaoStatus)
    {
        try {
            $url = $this->baseUrl . '/Fatura/inserirStatusFatura';
            
            $payload = [
                'Data' => [
                    'IdOrigem' => $idOrigem,
                    'NomeStatus' => $nomeStatus,
                    'DescricaoStatus' => $descricaoStatus
                ],
                'AccessToken' => $this->accessToken,
                'SystemCode' => $this->systemCode
            ];

            Log::info('MinisterioSaudeApiService - Enviando requisição para inserir status', [
                'url' => $url,
                'payload' => $payload
            ]);

            $response = Http::timeout(30)
                ->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('MinisterioSaudeApiService - Status inserido com sucesso', [
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'message' => $responseData['Message'] ?? 'Status incluído com sucesso'
                ];
            } else {
                Log::error('MinisterioSaudeApiService - Erro na requisição', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'Erro na comunicação com a API: ' . $response->status(),
                    'error' => $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('MinisterioSaudeApiService - Exceção ao inserir status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Consultar status da API do Ministério da Saúde
     */
    public function consultarStatusOp($idStatus = null, $idOrigem = null, $nomeStatus = null)
    {
        try {
            $url = $this->baseUrl . '/Fatura/ConsultarStatusOp';
            
            $params = [
                'AccessToken' => $this->accessToken
            ];

            if ($idStatus) {
                $params['IdStatus'] = $idStatus;
            }

            if ($idOrigem) {
                $params['IdOrigem'] = $idOrigem;
            }

            if ($nomeStatus) {
                $params['NomeStatus'] = $nomeStatus;
            }

            Log::info('MinisterioSaudeApiService - Consultando status', [
                'url' => $url,
                'params' => $params
            ]);

            $response = Http::timeout(30)
                ->get($url, $params);

            if ($response->successful()) {
                $responseData = $response->json();
                
                Log::info('MinisterioSaudeApiService - Status consultado com sucesso', [
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'data' => $responseData,
                    'message' => $responseData['Message'] ?? 'Consulta realizada com sucesso'
                ];
            } else {
                Log::error('MinisterioSaudeApiService - Erro na consulta', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'Erro na comunicação com a API: ' . $response->status(),
                    'error' => $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('MinisterioSaudeApiService - Exceção ao consultar status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Consultar endereço local na API do Ministério da Saúde
     */
    public function consultarEnderecoLocal(ConsultarEnderecoDTO $dto): array
    {
        try {
            $url = $this->baseUrl . config('ministerio_saude.endpoints.consultar_endereco_local');
            
            $params = $dto->toQueryParams();

            Log::info('MinisterioSaudeApiService - Consultando endereço local', [
                'url' => $url,
                'params' => $params
            ]);

            $response = Http::timeout(config('ministerio_saude.api.timeout', 30))
                ->get($url, $params);

            $statusCode = $response->status();
            $responseData = $response->json();

            if ($response->successful()) {
                Log::info('MinisterioSaudeApiService - Endereço consultado com sucesso', [
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'status_code' => $statusCode,
                    'data' => $responseData,
                    'message' => $responseData['Message'] ?? 'Consulta realizada com sucesso'
                ];
            } else {
                Log::warning('MinisterioSaudeApiService - Erro na consulta de endereço', [
                    'status' => $statusCode,
                    'response' => $responseData
                ]);

                return [
                    'success' => false,
                    'status_code' => $statusCode,
                    'data' => $responseData,
                    'message' => $responseData['Message'] ?? 'Erro na comunicação com a API: ' . $statusCode
                ];
            }

        } catch (\Exception $e) {
            Log::error('MinisterioSaudeApiService - Exceção ao consultar endereço local', [
                'dto' => $dto->toQueryParams(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'status_code' => 500,
                'data' => null,
                'message' => 'Erro interno: ' . $e->getMessage()
            ];
        }
    }
}
