<?php

namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;

use Domain\MinisterioSaude\Models\StatusOperador;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
use Illuminate\Database\Eloquent\Collection;

class StatusOperadorRepository extends BaseAbastractRepository implements StatusOperadorRepositoryInterface
{
    protected $model;

    public function __construct(StatusOperador $model)
    {
        parent::__construct($model);
    }

    public function list(array $filtros): Collection
    {
        return $this->model->get();
    }
}
