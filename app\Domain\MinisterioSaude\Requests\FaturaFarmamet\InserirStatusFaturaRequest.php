<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class InserirStatusFaturaRequest extends ApiFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'id_origem' => 'required|string|max:11',
            'nome_status' => 'required|string|max:40',
            'descricao_status' => 'required|string|max:240',
            'system_code' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'id_origem.required' => 'IdOrigem não informado',
            'id_origem.max' => 'IdOrigem deve ter no máximo 11 caracteres',
            'nome_status.required' => 'NomeStatus não informado',
            'nome_status.max' => 'NomeStatus deve ter no máximo 40 caracteres',
            'descricao_status.required' => 'DescricaoStatus não informado',
            'descricao_status.max' => 'DescricaoStatus deve ter no máximo 240 caracteres',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'id_origem' => 'ID de Origem',
            'nome_status' => 'Nome do Status',
            'descricao_status' => 'Descrição do Status',
            'system_code' => 'Código do Sistema'
        ];
    }
}
