<?php

namespace Domain\MinisterioSaude\Repositories;

use Domain\MinisterioSaude\Models\EnderecoLocal;
use Domain\MinisterioSaude\DTOs\EnderecoResponseDTO;
use Illuminate\Database\Eloquent\Collection;

interface EnderecoLocalRepositoryInterface
{
    public function findByGestorELocal(string $idGestor, string $idLocal): ?EnderecoLocal;
    public function findByCodigoEndereco(string $codigoEndereco): ?EnderecoLocal;
    public function findAll(): Collection;
    public function findAtivos(): Collection;
    public function findSemGeolocalizacao(): Collection;
    public function create(array $data): EnderecoLocal;
    public function update(EnderecoLocal $endereco, array $data): EnderecoLocal;
    public function delete(EnderecoLocal $endereco): bool;
    public function existePorGestorELocal(string $idGestor, string $idLocal): bool;
    public function marcarComoSincronizado(EnderecoLocal $endereco): EnderecoLocal;
}

class EnderecoLocalRepository implements EnderecoLocalRepositoryInterface
{
    public function findByGestorELocal(string $idGestor, string $idLocal): ?EnderecoLocal
    {
        return EnderecoLocal::where('id_gestor', $idGestor)
                          ->where('id_local', $idLocal)
                          ->first();
    }

    public function findByCodigoEndereco(string $codigoEndereco): ?EnderecoLocal
    {
        return EnderecoLocal::where('codigo_endereco', $codigoEndereco)->first();
    }

    public function findAll(): Collection
    {
        return EnderecoLocal::orderBy('created_at', 'desc')->get();
    }

    public function findAtivos(): Collection
    {
        return EnderecoLocal::ativos()->orderBy('created_at', 'desc')->get();
    }

    public function findSemGeolocalizacao(): Collection
    {
        return EnderecoLocal::semGeolocalizacao()
                          ->ativos()
                          ->orderBy('created_at', 'desc')
                          ->get();
    }

    public function create(array $data): EnderecoLocal
    {
        return EnderecoLocal::create($data);
    }

    public function update(EnderecoLocal $endereco, array $data): EnderecoLocal
    {
        $endereco->update($data);
        return $endereco->fresh();
    }

    public function delete(EnderecoLocal $endereco): bool
    {
        return $endereco->delete();
    }

    public function existePorGestorELocal(string $idGestor, string $idLocal): bool
    {
        return EnderecoLocal::where('id_gestor', $idGestor)
                          ->where('id_local', $idLocal)
                          ->exists();
    }

    public function marcarComoSincronizado(EnderecoLocal $endereco): EnderecoLocal
    {
        $endereco->marcarComoSincronizado();
        return $endereco->fresh();
    }

    public function criarOuAtualizarDoApi(EnderecoResponseDTO $enderecoDTO, string $idGestor, string $idLocal): EnderecoLocal
    {
        $endereco = $this->findByGestorELocal($idGestor, $idLocal);

        $dados = [
            'id_gestor' => $idGestor,
            'id_local' => $idLocal,
            'codigo_endereco' => $enderecoDTO->codigoEndereco,
            'nome_local' => $enderecoDTO->nomeLocal,
            'cep' => $enderecoDTO->endereco->cep,
            'nome_tipo_logradouro' => $enderecoDTO->endereco->nomeTipoLogradouro,
            'nome_logradouro' => $enderecoDTO->endereco->nomeLogradouro,
            'numero' => $enderecoDTO->endereco->numero,
            'complemento' => $enderecoDTO->endereco->complemento,
            'nome_bairro' => $enderecoDTO->endereco->nomeBairro,
            'nome_municipio' => $enderecoDTO->endereco->nomeMunicipio,
            'uf' => $enderecoDTO->endereco->uf,
            'flag_registro' => true,
            'dt_ultima_sincronizacao' => now()
        ];

        if ($endereco) {
            return $this->update($endereco, $dados);
        } else {
            return $this->create($dados);
        }
    }
}
