<?php

namespace Domain\IceExchange\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Domain\NOTFIS\Models\NotfisVolume;

class IceStock extends Model
{
    protected $table = 'ice_change_stock';

    protected $fillable = [
        'warehouse_id',
        'notfis_volume_id',
        'status',
        'deleted_at',
    ];

    protected $casts = [
        'warehouse_id' => 'integer',
        'notfis_volume_id' => 'integer',
        'deleted_at' => 'datetime',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(IceWarehouse::class, 'warehouse_id');
    }

    public function notfisVolume(): BelongsTo
    {
        return $this->belongsTo(NotfisVolume::class, 'notfis_volume_id');
    }

    /**
     * Buscar o status mais recente de um volume (apenas registros ativos)
     */
    public static function getLastStatusForVolume(int $volumeId): ?string
    {
        $lastRecord = self::where('notfis_volume_id', $volumeId)
            ->whereNull('deleted_at') // Apenas registros ativos
            ->orderBy('created_at', 'desc')
            ->first();

        return $lastRecord ? $lastRecord->status : null;
    }

    /**
     * Buscar o último registro ativo de um volume
     */
    public static function getLastActiveRecordForVolume(int $volumeId): ?self
    {
        return self::where('notfis_volume_id', $volumeId)
            ->whereNull('deleted_at')
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Verificar se um volume está atualmente no armazém
     */
    public static function isVolumeInWarehouse(int $volumeId): bool
    {
        $lastRecord = self::getLastActiveRecordForVolume($volumeId);
        return $lastRecord && $lastRecord->status === 'in';
    }

    /**
     * Verificar se um volume pode entrar no armazém
     */
    public static function canVolumeEnter(int $volumeId): bool
    {
        $lastRecord = self::getLastActiveRecordForVolume($volumeId);
        // Pode entrar se não tem registro ativo (primeira vez) ou se o último registro está 'out'
        return !$lastRecord || $lastRecord->status === 'out';
    }

    /**
     * Verificar se um volume pode sair do armazém
     */
    public static function canVolumeExit(int $volumeId): bool
    {
        $lastRecord = self::getLastActiveRecordForVolume($volumeId);
        // Só pode sair se tem registro ativo com status 'in'
        return $lastRecord && $lastRecord->status === 'in';
    }

    /**
     * Buscar armazém atual de um volume
     */
    public static function getCurrentWarehouseForVolume(int $volumeId): ?int
    {
        $lastRecord = self::where('notfis_volume_id', $volumeId)
            ->where('status', 'in')
            ->whereNull('deleted_at') // Apenas registros ativos
            ->orderBy('created_at', 'desc')
            ->first();

        return $lastRecord ? $lastRecord->warehouse_id : null;
    }

    /**
     * Registrar entrada de volume no armazém
     */
    public static function registerEntry(int $volumeId, int $warehouseId): self
    {
        return self::create([
            'notfis_volume_id' => $volumeId,
            'warehouse_id' => $warehouseId,
            'status' => 'in',
            'deleted_at' => null,
        ]);
    }

    /**
     * Registrar saída de volume do armazém
     * Marca o registro atual como 'out' e define deleted_at
     */
    public static function registerExit(int $volumeId): bool
    {
        $activeRecord = self::getLastActiveRecordForVolume($volumeId);
        
        if (!$activeRecord || $activeRecord->status !== 'in') {
            return false; // Volume não está no armazém
        }

        $activeRecord->update([
            'status' => 'out',
            'deleted_at' => now(),
        ]);

        return true;
    }
}
