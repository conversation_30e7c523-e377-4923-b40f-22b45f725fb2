<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\FaturaFarmanet;
use App\Models\FaturaFarmanetItem;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RecebimentoFaturasFarmanetService
{
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
            'verify' => false,
        ]);
        
        // Para Faturas Farmanet, usar a base URL principal (sem /planejamento.servico)
        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
    }

    /**
     * Consulta faturas do Farmanet no Ministério da Saúde
     *
     * @param array $params
     * @return array
     */
    public function consultarFaturas(array $params): array
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_faturas');
            $accessToken = $params['access_token'];
            $url = $this->baseUrl . $endpoint . '?AccessToken=' . $accessToken;
            
            Log::info('RecebimentoFaturasFarmanetService - Consultando faturas via API real', [
                'url' => $url,
                'params' => collect($params)->except(['access_token'])->toArray()
            ]);

            // Payload para consultar faturas (não criar faturas)
            $payload = [
                'Data' => [
                    'IdGestor' => $params['id_gestor'],
                    'AnoReferencia' => $params['ano_referencia'],
                    'MesReferencia' => $params['mes_referencia'] ?? null,
                    'IdProgramaSaude' => $params['codigo_programa'] ?? null,
                    'StatusFatura' => $params['status_fatura'] ?? null,
                    'DataInicio' => $params['data_inicio'] ?? null,
                    'DataFim' => $params['data_fim'] ?? null,
                ],
                'AccessToken' => $accessToken,
                'SystemCode' => config('ministerio_saude.api.system_code', 'IBL_LOGISTICS')
            ];

            // Remover campos nulos do Data
            $payload['Data'] = array_filter($payload['Data'], function($value) {
                return $value !== null;
            });

            $response = $this->client->put($url, [
                'json' => $payload,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            Log::info('RecebimentoFaturasFarmanetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $data['ResultCode'] ?? null,
                'message' => $data['Message'] ?? null
            ]);

            return [
                'success' => true,
                'data' => $data,
                'message' => 'Consulta de faturas Farmanet realizada com sucesso via API real do Ministério.',
                'total_faturas' => count($data['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
            
            Log::error('RecebimentoFaturasFarmanetService - Erro na consulta', [
                'message' => $e->getMessage(),
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $params
            ]);

            return [
                'success' => false,
                'message' => $errorMessage,
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Processa e armazena faturas recebidas da API
     *
     * @param array $faturas
     * @param array $params
     * @return void
     */
    private function processarEArmazenarFaturas(array $faturas, array $params): void
    {
        foreach ($faturas as $faturaData) {
            try {
                // Buscar ou criar a fatura
                $fatura = FaturaFarmanet::updateOrCreate(
                    [
                        'numero_fatura' => $faturaData['numero_fatura'],
                        'id_gestor' => $params['id_gestor']
                    ],
                    [
                        'codigo_programa' => $faturaData['codigo_programa'] ?? $params['codigo_programa'],
                        'status_fatura' => $faturaData['status'] ?? 1,
                        'data_emissao' => isset($faturaData['data_emissao']) ? Carbon::parse($faturaData['data_emissao']) : null,
                        'data_vencimento' => isset($faturaData['data_vencimento']) ? Carbon::parse($faturaData['data_vencimento']) : null,
                        'data_pagamento' => isset($faturaData['data_pagamento']) ? Carbon::parse($faturaData['data_pagamento']) : null,
                        'valor_total' => $faturaData['valor_total'] ?? null,
                        'valor_pago' => $faturaData['valor_pago'] ?? null,
                        'observacoes' => $faturaData['observacoes'] ?? null,
                        'data_ultima_atualizacao' => Carbon::now(),
                        'dados_originais' => json_encode($faturaData)
                    ]
                );

                // Processar itens da fatura
                if (isset($faturaData['itens']) && is_array($faturaData['itens'])) {
                    $this->processarItensFatura($fatura, $faturaData['itens']);
                }

                Log::info('Fatura Farmanet processada', [
                    'fatura_id' => $fatura->id,
                    'numero_fatura' => $fatura->numero_fatura,
                    'total_itens' => count($faturaData['itens'] ?? [])
                ]);

            } catch (\Exception $e) {
                Log::error('Erro ao processar fatura Farmanet', [
                    'fatura_data' => $faturaData,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Processa itens de uma fatura
     *
     * @param FaturaFarmanet $fatura
     * @param array $itens
     * @return void
     */
    private function processarItensFatura(FaturaFarmanet $fatura, array $itens): void
    {
        // Limpar itens existentes para recriar
        $fatura->itens()->delete();

        foreach ($itens as $itemData) {
            try {
                FaturaFarmanetItem::create([
                    'fatura_farmanet_id' => $fatura->id,
                    'codigo_medicamento' => $itemData['codigo_medicamento'] ?? null,
                    'nome_medicamento' => $itemData['nome_medicamento'] ?? null,
                    'codigo_farmaco' => $itemData['codigo_farmaco'] ?? null,
                    'quantidade' => $itemData['quantidade'] ?? 0,
                    'valor_unitario' => $itemData['valor_unitario'] ?? null,
                    'valor_total' => $itemData['valor_total'] ?? null,
                    'desconto_aplicado' => $itemData['desconto_aplicado'] ?? null,
                    'observacoes' => $itemData['observacoes'] ?? null,
                    'dados_originais' => json_encode($itemData)
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao processar item da fatura Farmanet', [
                    'fatura_id' => $fatura->id,
                    'item_data' => $itemData,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Confirma recebimento de uma fatura
     *
     * @param string $numeroFatura
     * @param string $idGestor
     * @param string $accessToken
     * @param array $dadosRecebimento
     * @return array
     */
    public function confirmarRecebimento(string $numeroFatura, string $idGestor, string $accessToken, array $dadosRecebimento = []): array
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_faturas');
            $url = $this->baseUrl . $endpoint . '/' . $numeroFatura . '/confirmar-recebimento';
            
            Log::info('RecebimentoFaturasFarmanetService - Confirmando recebimento', [
                'url' => $url,
                'numero_fatura' => $numeroFatura,
                'id_gestor' => $idGestor
            ]);

            $payload = array_merge([
                'id_gestor' => $idGestor,
                'data_recebimento' => Carbon::now()->format('Y-m-d H:i:s')
            ], $dadosRecebimento);

            $response = $this->client->post($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $payload
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            Log::info('RecebimentoFaturasFarmanetService - Recebimento confirmado', [
                'status_code' => $response->getStatusCode(),
                'numero_fatura' => $numeroFatura
            ]);

            // Atualizar status local da fatura
            $this->atualizarStatusFaturaLocal($numeroFatura, $idGestor, 2); // Status 2 = Processada

            return [
                'success' => true,
                'data' => $data,
                'message' => 'Recebimento de fatura confirmado com sucesso',
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            Log::error('RecebimentoFaturasFarmanetService - Erro ao confirmar recebimento', [
                'message' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'numero_fatura' => $numeroFatura
            ]);

            return [
                'success' => false,
                'error' => 'Erro ao confirmar recebimento da fatura: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('RecebimentoFaturasFarmanetService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'numero_fatura' => $numeroFatura
            ]);

            return [
                'success' => false,
                'error' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Atualiza o status de uma fatura localmente
     *
     * @param string $numeroFatura
     * @param string $idGestor
     * @param int $novoStatus
     * @return void
     */
    private function atualizarStatusFaturaLocal(string $numeroFatura, string $idGestor, int $novoStatus): void
    {
        try {
            $fatura = FaturaFarmanet::where('numero_fatura', $numeroFatura)
                                   ->where('id_gestor', $idGestor)
                                   ->first();

            if ($fatura) {
                $fatura->update([
                    'status_fatura' => $novoStatus,
                    'data_ultima_atualizacao' => Carbon::now()
                ]);

                Log::info('Status da fatura Farmanet atualizado localmente', [
                    'fatura_id' => $fatura->id,
                    'numero_fatura' => $numeroFatura,
                    'novo_status' => $novoStatus
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Erro ao atualizar status da fatura localmente', [
                'numero_fatura' => $numeroFatura,
                'id_gestor' => $idGestor,
                'novo_status' => $novoStatus,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Busca faturas locais com filtros
     *
     * @param array $filters
     * @return array
     */
    public function buscarFaturasLocais(array $filters = []): array
    {
        $query = FaturaFarmanet::with('itens');

        if (isset($filters['id_gestor'])) {
            $query->where('id_gestor', $filters['id_gestor']);
        }

        if (isset($filters['codigo_programa'])) {
            $query->where('codigo_programa', $filters['codigo_programa']);
        }

        if (isset($filters['status_fatura'])) {
            $query->where('status_fatura', $filters['status_fatura']);
        }

        if (isset($filters['numero_fatura'])) {
            $query->where('numero_fatura', 'like', '%' . $filters['numero_fatura'] . '%');
        }

        if (isset($filters['data_inicio']) && isset($filters['data_fim'])) {
            $query->whereBetween('data_emissao', [$filters['data_inicio'], $filters['data_fim']]);
        }

        $faturas = $query->orderBy('data_emissao', 'desc')->get();

        return [
            'success' => true,
            'faturas' => $faturas->toArray(),
            'total' => $faturas->count(),
            'timestamp' => Carbon::now()->toISOString()
        ];
    }

    /**
     * Lista os status possíveis para uma fatura
     *
     * @return array
     */
    public function obterStatusPossiveis(): array
    {
        return [
            1 => 'Pendente',
            2 => 'Processada',
            3 => 'Paga',
            4 => 'Cancelada'
        ];
    }
}
