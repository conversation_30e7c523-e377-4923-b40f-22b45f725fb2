<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasGsnetItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_gsnet_itens', function (Blueprint $table) {
            $table->id();
            
            // Relacionamento com a fatura
            $table->foreignId('fatura_gsnet_id')->constrained('faturas_gsnet')->onDelete('cascade');
            
            // Identificação do item/serviço
            $table->string('codigo_item', 50)->comment('Código do item/serviço');
            $table->string('descricao_item', 500)->comment('Descrição detalhada do item');
            $table->string('unidade_medida', 10)->nullable()->comment('Unidade de medida');
            
            // Quantidades e valores
            $table->decimal('quantidade', 10, 3)->comment('Quantidade do item');
            $table->decimal('valor_unitario', 10, 4)->comment('Valor unitário');
            $table->decimal('valor_total_item', 15, 2)->comment('Valor total do item');
            $table->decimal('percentual_desconto', 5, 2)->default(0)->comment('Percentual de desconto');
            $table->decimal('valor_desconto_item', 15, 2)->default(0)->comment('Valor do desconto');
            
            // Impostos e taxas
            $table->decimal('percentual_icms', 5, 2)->nullable()->comment('Percentual ICMS');
            $table->decimal('valor_icms', 15, 2)->nullable()->comment('Valor ICMS');
            $table->decimal('percentual_ipi', 5, 2)->nullable()->comment('Percentual IPI');
            $table->decimal('valor_ipi', 15, 2)->nullable()->comment('Valor IPI');
            
            // Informações complementares
            $table->string('ncm', 20)->nullable()->comment('Código NCM');
            $table->string('cfop', 10)->nullable()->comment('CFOP');
            $table->text('observacoes_item')->nullable()->comment('Observações do item');
            
            // Dados originais da API
            $table->longText('dados_originais')->nullable()->comment('Dados originais da API');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices
            $table->index('codigo_item');
            $table->index(['fatura_gsnet_id', 'codigo_item']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_gsnet_itens');
    }
}
