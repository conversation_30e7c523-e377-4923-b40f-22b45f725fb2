<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePedidosFarmanetItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('pedidos_farmanet_itens', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pedido_id')->comment('FK para pedidos_farmanet');
            $table->string('protocolo_id_gsnet', 40)->comment('Protocolo do pedido');
            $table->integer('codigo_item_material')->comment('Código do item material');
            $table->decimal('quantidade_item_material', 15, 3)->comment('Quantidade do item');
            $table->decimal('preco_medio_unitario', 25, 7)->nullable()->comment('Preço médio unitário quando disponível');
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Foreign key
            $table->foreign('pedido_id')->references('id')->on('pedidos_farmanet')->onDelete('cascade');
            
            // Indexes
            $table->index(['protocolo_id_gsnet']);
            $table->index(['codigo_item_material']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('pedidos_farmanet_itens');
    }
}
