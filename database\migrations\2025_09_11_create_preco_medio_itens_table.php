<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrecoMedioItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('preco_medio_itens', function (Blueprint $table) {
            $table->id();
            $table->integer('cd_item')->unique()->comment('Código do item no GSNET');
            $table->decimal('preco_medio_unitario', 25, 7)->comment('Preço médio unitário');
            $table->datetime('data_consulta')->comment('Data da última consulta na API');
            $table->datetime('data_atualizacao')->comment('Data da última atualização do preço');
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['cd_item']);
            $table->index(['data_consulta']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('preco_medio_itens');
    }
}
