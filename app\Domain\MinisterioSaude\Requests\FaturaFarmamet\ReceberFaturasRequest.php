<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class ReceberFaturasRequest extends ApiFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dt_documento' => 'nullable|date_format:d/m/Y|max:50',
            'nr_documento' => 'nullable|string|max:100',
            'vl_documento' => 'nullable|string|max:50',
            'id_programa_saude' => 'nullable|string|max:50',
            'id_local_destino' => 'nullable|string|max:50',
            'cd_pedido' => 'nullable|string|max:100',
            'itens' => 'nullable|array|min:1',
            'itens.*.codigo_item' => 'nullable|string|max:100',
            'itens.*.quantidade' => 'nullable|string|max:50',
            'system_code' => 'nullable|string|max:100'
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'dt_documento.date_format' => 'A data do documento deve estar no formato d/m/Y',
            'dt_documento.max' => 'A data do documento deve ter no máximo 50 caracteres',
            'nr_documento.string' => 'O número do documento deve ser uma string',
            'nr_documento.max' => 'O número do documento deve ter no máximo 100 caracteres',
            'vl_documento.string' => 'O valor do documento deve ser uma string',
            'vl_documento.max' => 'O valor do documento deve ter no máximo 50 caracteres',
            'id_programa_saude.string' => 'O ID do programa de saúde deve ser uma string',
            'id_programa_saude.max' => 'O ID do programa de saúde deve ter no máximo 50 caracteres',
            'id_local_destino.string' => 'O ID do local de destino deve ser uma string',
            'id_local_destino.max' => 'O ID do local de destino deve ter no máximo 50 caracteres',
            'cd_pedido.string' => 'O código do pedido deve ser uma string',
            'cd_pedido.max' => 'O código do pedido deve ter no máximo 100 caracteres',
            'itens.min' => 'Deve haver pelo menos um item',
            'itens.*.codigo_item.required' => 'O código do item é obrigatório',
            'itens.*.codigo_item.string' => 'O código do item deve ser uma string',
            'itens.*.codigo_item.max' => 'O código do item deve ter no máximo 100 caracteres',
            'itens.*.quantidade.required' => 'A quantidade do item é obrigatória',
            'itens.*.quantidade.string' => 'A quantidade do item deve ser uma string',
            'itens.*.quantidade.max' => 'A quantidade do item deve ter no máximo 50 caracteres',
            'system_code.string' => 'O código do sistema deve ser uma string',
            'system_code.max' => 'O código do sistema deve ter no máximo 100 caracteres'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'dt_documento' => 'Data do Documento',
            'nr_documento' => 'Número do Documento',
            'vl_documento' => 'Valor do Documento',
            'id_programa_saude' => 'ID do Programa de Saúde',
            'id_local_destino' => 'ID do Local de Destino',
            'cd_pedido' => 'Código do Pedido',
            'itens' => 'Itens',
            'itens.*.codigo_item' => 'Código do Item',
            'itens.*.quantidade' => 'Quantidade do Item',
            'system_code' => 'Código do Sistema'
        ];
    }
}
