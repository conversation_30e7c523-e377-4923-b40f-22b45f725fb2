<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ice_change_stock', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('warehouse_id');
            $table->unsignedBigInteger('notfis_volume_id'); // ID do volume
            $table->enum('status', ['in', 'out']);
            $table->timestamps();

            // Índices para performance
            $table->index(['notfis_volume_id', 'created_at']);
            $table->index(['warehouse_id', 'status']);
            
            // Chaves estrangeiras
            $table->foreign('warehouse_id')->references('id')->on('ice_change_warehouse');
            $table->foreign('notfis_volume_id')->references('id')->on('notfis_volumes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ice_change_stock');
    }
};
