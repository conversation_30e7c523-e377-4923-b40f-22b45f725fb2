<?php

namespace Domain\MinisterioSaude\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnderecoLocalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'id_gestor' => $this->id_gestor,
            'id_local' => $this->id_local,
            'codigo_endereco' => $this->codigo_endereco,
            'nome_local' => $this->nome_local,
            'endereco' => [
                'cep' => $this->cep,
                'cep_formatado' => $this->cep_formatado,
                'nome_tipo_logradouro' => $this->nome_tipo_logradouro,
                'nome_logradouro' => $this->nome_logradouro,
                'numero' => $this->numero,
                'complemento' => $this->complemento,
                'nome_bairro' => $this->nome_bairro,
                'nome_municipio' => $this->nome_municipio,
                'uf' => $this->uf,
                'endereco_completo' => $this->endereco_completo
            ],
            'geolocalizacao' => [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
                'distancia_km' => $this->distancia_km,
                'tem_geolocalizacao' => $this->tem_geolocalizacao
            ],
            'controle' => [
                'flag_registro' => $this->flag_registro,
                'dt_ultima_sincronizacao' => $this->dt_ultima_sincronizacao?->format('Y-m-d H:i:s'),
                'created_at' => $this->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
            ]
        ];
    }
}
