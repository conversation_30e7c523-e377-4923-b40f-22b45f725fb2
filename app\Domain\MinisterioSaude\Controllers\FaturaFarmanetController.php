<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Traits\HasLog;
use Illuminate\Http\JsonResponse;
use App\Controllers\Controller;
// Requests
use Domain\MinisterioSaude\Requests\FaturaFarmamet\InserirStatusFaturaRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarStatusOpRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarEnderecoRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarFaturasRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\AtualizarStatusFaturaRequest;
use Domain\MinisterioSaude\Requests\FaturaFarmamet\ReceberFaturasRequest;
// Inputs
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\ConsultarStatusInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\CriarStatusInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input\ConsultaEnderecoInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\AtualizarStatusFaturaInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ReceberFaturasInput;
// Services
use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\StatusOperadorService;
use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\FaturaGsnetService;

class FaturaFarmanetController extends Controller
{
    use HasLog;

    private StatusOperadorService $statusService;
    private EnderecoLocalService $enderecoService;
    private FaturaGsnetService $faturaService;

    public function __construct(
        StatusOperadorService $statusService,
        EnderecoLocalService $enderecoService,
        FaturaGsnetService $faturaService
    ) {
        $this->statusService = $statusService;
        $this->enderecoService = $enderecoService;
        $this->faturaService = $faturaService;
    }

    /**
     * API 1.1 - Inserir Status na Fatura
     *
     * @param InserirStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
    {
        try {
            $result = $this->statusService->criarStatus(CriarStatusInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@inserirStatusFatura - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 1.2 - Consultar Status do Operador
     *
     * @param ConsultarStatusOpRequest $request
     * @return JsonResponse
     */
    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
    {
        try {
            $result = $this->statusService->consultarStatus(ConsultarStatusInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@consultarStatusOp - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 1.3 - Consultar Endereço Local
     *
     * @param ConsultarEnderecoRequest $request
     * @return JsonResponse
     */
    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
    {
        try {
            $result = $this->enderecoService->consultarEndereco(ConsultaEnderecoInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@consultarEndereco - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 2.1 - Consultar Faturas (Recebe)
     *
     * @param ConsultarFaturasRequest $request
     * @return JsonResponse
     */
    public function consultarFaturas(ConsultarFaturasRequest $request): JsonResponse
    {
        try {
            $result = $this->faturaService->consultarFaturas(ConsultaFaturaInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@consultarFaturas - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 2.2 - Atualizar Status da Fatura (Envio)
     *
     * @param AtualizarStatusFaturaRequest $request
     * @return JsonResponse
     */
    public function atualizarStatusFatura(AtualizarStatusFaturaRequest $request): JsonResponse
    {
        try {
            $result = $this->faturaService->atualizarStatusFatura(AtualizarStatusFaturaInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@atualizarStatusFatura - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 3.3 - Recebimento Faturas Farmanet
     *
     * Serviço utilizado para gerar Fatura do pedido Farmanet no GSNET
     *
     * @param ReceberFaturasRequest $request
     * @return JsonResponse
     */
    public function receberFaturas(ReceberFaturasRequest $request): JsonResponse
    {
        try {
            $result = $this->faturaService->receberFaturas(ReceberFaturasInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('FaturaFarmanetController@receberFaturas - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }
}
