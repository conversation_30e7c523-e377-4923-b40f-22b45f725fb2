<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeletedAtToIceChangeStockTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ice_change_stock', function (Blueprint $table) {
            $table->timestamp('deleted_at')->nullable()->after('status');
            $table->index(['notfis_volume_id', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ice_change_stock', function (Blueprint $table) {
            $table->dropIndex(['notfis_volume_id', 'deleted_at']);
            $table->dropColumn('deleted_at');
        });
    }
}
