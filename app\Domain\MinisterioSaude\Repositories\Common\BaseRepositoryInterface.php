<?php

namespace Domain\MinisterioSaude\Repositories\Common;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

interface BaseRepositoryInterface
{
    public function list(array $filtros): Collection;
    public function get(int $id): Model;
    public function getWhere(callable $conditions): Collection;
    public function getOneWhere(callable $conditions): ?Model;
    public function findBy(array $conditions): Model;
    public function store(array $request): Collection;
    public function storeArray(array $data): Collection;
    public function storeSingleData(array $data): Collection;
    public function updateOrStore(array $attributes, ?array $values): Collection;
    public function update(array $request, int $id): bool;
    public function updateArray(array $data, int $id): bool;
    public function destroy(int $id): bool;
    public function searchByCustomField(string $field, string $value): Collection;
}
