# APIs Farmanet - Sistema Farmacêutico

## Resumo das APIs Implementadas

### API 3.1 - Consultar Pedidos Farmanet (Recebe)
**Endpoint:** `POST /api/ministerio-saude/sp/pedidos-farmanet/consultar`

**Descrição:** Consulta pedidos farmacêuticos do programa Farmanet junto ao Ministério da Saúde.

**Parâmetros Obrigatórios:**
- `id_gestor` (string, max 22)
- `ano_referencia` (integer, YYYY)
- `access_token` (string, max 40)

**Parâmetros Opcionais:**
- `codigo_programa` (integer: 5,24,25,28)
- `mes_referencia` (integer: 1-12)
- `ano_periodo_referencia` (integer, YYYY)

**Endpoints Auxiliares:**
- `GET /api/ministerio-saude/sp/pedidos-farmanet/locais` - Buscar pedidos locais
- `GET /api/ministerio-saude/sp/pedidos-farmanet/info` - Informações da API

---

### API 3.2 - Atualizar Pedidos Farmanet (Envio)
**Endpoint:** `PUT /api/ministerio-saude/sp/pedidos-farmanet/atualizar`

**Descrição:** Atualiza status de pedidos farmacêuticos do programa Farmanet junto ao Ministério da Saúde.

**Parâmetros Obrigatórios:**
- `id_gestor` (string, max 22)
- `id_pedido_ms` (string, max 50)
- `status_pedido` (integer: 1-5)
- `data_atualizacao` (datetime: Y-m-d H:i:s)
- `access_token` (string, max 40)

**Parâmetros Opcionais:**
- `observacoes` (string, max 1000)

**Status Possíveis:**
- 1: Pendente
- 2: Em Processamento
- 3: Aprovado
- 4: Rejeitado
- 5: Cancelado

**Endpoints Auxiliares:**
- `POST /api/ministerio-saude/sp/pedidos-farmanet/validar-atualizacao` - Validar atualização
- `GET /api/ministerio-saude/sp/pedidos-farmanet/status` - Listar status possíveis
- `GET /api/ministerio-saude/sp/pedidos-farmanet/atualizar/info` - Informações da API

---

### API 3.3 - Recebimento Faturas Farmanet (Recebe)
**Endpoint:** `POST /api/ministerio-saude/sp/faturas-farmanet/consultar`

**Descrição:** Consulta e gerencia faturas farmacêuticas do programa Farmanet junto ao Ministério da Saúde.

**Parâmetros Obrigatórios:**
- `id_gestor` (string, max 22)
- `ano_referencia` (integer, YYYY)
- `access_token` (string, max 40)

**Parâmetros Opcionais:**
- `codigo_programa` (integer: 5,24,25,28)
- `mes_referencia` (integer: 1-12)
- `numero_fatura` (string, max 50)
- `status_fatura` (integer: 1-4)
- `data_inicio` (date: Y-m-d)
- `data_fim` (date: Y-m-d)

**Status Possíveis:**
- 1: Pendente
- 2: Processada
- 3: Paga
- 4: Cancelada

**Endpoints Auxiliares:**
- `POST /api/ministerio-saude/sp/faturas-farmanet/confirmar-recebimento` - Confirmar recebimento
- `GET /api/ministerio-saude/sp/faturas-farmanet/locais` - Buscar faturas locais
- `GET /api/ministerio-saude/sp/faturas-farmanet/status` - Listar status possíveis
- `GET /api/ministerio-saude/sp/faturas-farmanet/info` - Informações da API

---

### API 3.4 - Obter Preço Médio de Itens (Recebe)
**Endpoint:** `POST /api/ministerio-saude/sp/preco-medio-itens/obter`

**Descrição:** Consulta preços médios de medicamentos e fármacos dos programas do Ministério da Saúde.

**Parâmetros Obrigatórios:**
- `codigo_programa` (integer: 5,24,25,28)
- `ano_referencia` (integer, YYYY)
- `access_token` (string, max 40)

**Parâmetros Opcionais:**
- `mes_referencia` (integer: 1-12)
- `codigo_medicamento` (string, max 20)
- `codigo_farmaco` (string, max 20)
- `estado_origem` (string, 2 chars)

**Endpoints Auxiliares:**
- `GET /api/ministerio-saude/sp/preco-medio-itens/locais` - Buscar preços locais
- `POST /api/ministerio-saude/sp/preco-medio-itens/estatisticas` - Obter estatísticas por programa
- `POST /api/ministerio-saude/sp/preco-medio-itens/comparar` - Comparar preços entre períodos
- `GET /api/ministerio-saude/sp/preco-medio-itens/programas` - Listar programas disponíveis
- `GET /api/ministerio-saude/sp/preco-medio-itens/info` - Informações da API

---

## Programas Disponíveis

- **5:** Diabetes
- **24:** Dose Certa
- **25:** Saúde da Mulher
- **28:** Arboviroses

---

## Estrutura do Banco de Dados

### Tabelas Criadas:
1. `pedidos_farmanet` - Armazena os pedidos farmacêuticos
2. `pedidos_farmanet_itens` - Itens dos pedidos farmacêuticos
3. `faturas_farmanet` - Armazena as faturas farmacêuticas
4. `faturas_farmanet_itens` - Itens das faturas farmacêuticas
5. `preco_medio_itens` - Preços médios de medicamentos e fármacos

### Connection:
Todas as tabelas utilizam a conexão `ministerio_saude_sp` configurada no Laravel.

---

## Estrutura do Código

### Models:
- `PedidoFarmanet`
- `PedidoFarmanetItem`
- `FaturaFarmanet`
- `FaturaFarmanetItem`
- `PrecoMedioItem`

### Services:
- `ConsultarPedidosFarmanetService`
- `AtualizarPedidoFarmanetService`
- `RecebimentoFaturasFarmanetService`
- `ObterPrecoMedioItensService`

### Controllers:
- `ConsultarPedidosFarmanetController`
- `AtualizarPedidoFarmanetController`
- `RecebimentoFaturasFarmanetController`
- `ObterPrecoMedioItensController`

### Request Validators:
- `ConsultarPedidosFarmanetRequest`
- `AtualizarPedidoFarmanetRequest`
- `RecebimentoFaturasFarmanetRequest`
- `ObterPrecoMedioItensRequest`

---

## Recursos Implementados

✅ **Consulta de Pedidos:** Sistema completo de consulta e armazenamento de pedidos Farmanet
✅ **Atualização de Status:** Atualização controlada de status de pedidos com validação de regras de negócio
✅ **Gestão de Faturas:** Consulta e confirmação de recebimento de faturas farmacêuticas
✅ **Preços Médios:** Sistema de consulta de preços com estatísticas e comparações
✅ **Persistência Local:** Todas as consultas são armazenadas localmente para histórico
✅ **Logs Completos:** Sistema de logs detalhado para auditoria
✅ **Validação Robusta:** Validação completa de parâmetros e regras de negócio
✅ **APIs Auxiliares:** Endpoints para consultas locais e informações

---

## Exemplo de Uso

```bash
# Consultar Pedidos Farmanet
curl -X POST "http://localhost/api/ministerio-saude/sp/pedidos-farmanet/consultar" \
  -H "Content-Type: application/json" \
  -d '{
    "id_gestor": "12345678901234567890",
    "codigo_programa": 5,
    "ano_referencia": 2024,
    "mes_referencia": 9,
    "access_token": "seu_token_aqui"
  }'
```

---

**Status:** ✅ Implementação Completa - Pronto para Testes
