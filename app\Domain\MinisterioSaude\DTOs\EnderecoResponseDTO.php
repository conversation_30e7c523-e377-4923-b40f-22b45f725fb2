<?php

namespace Domain\MinisterioSaude\DTOs;

class EnderecoResponseDTO
{
    public string $codigoEndereco;
    public string $nomeLocal;
    public EnderecoDTO $endereco;

    public function __construct(array $data)
    {
        $this->codigoEndereco = $data['CodigoEndereco'] ?? '';
        $this->nomeLocal = $data['NomeLocal'] ?? '';
        $this->endereco = new EnderecoDTO($data['Endereco'] ?? []);
    }

    public function toArray(): array
    {
        return [
            'CodigoEndereco' => $this->codigoEndereco,
            'NomeLocal' => $this->nomeLocal,
            'Endereco' => $this->endereco->toArray()
        ];
    }
}

class EnderecoDTO
{
    public string $cep;
    public string $nomeTipoLogradouro;
    public string $nomeLogradouro;
    public ?string $numero;
    public ?string $complemento;
    public string $nomeBairro;
    public string $nomeMunicipio;
    public string $uf;

    public function __construct(array $data)
    {
        $this->cep = $data['Cep'] ?? '';
        $this->nomeTipoLogradouro = $data['NomeTipoLogradouro'] ?? '';
        $this->nomeLogradouro = $data['NomeLogradouro'] ?? '';
        $this->numero = $data['Numero'] ?? null;
        $this->complemento = $data['Complemento'] ?? null;
        $this->nomeBairro = $data['NomeBairro'] ?? '';
        $this->nomeMunicipio = $data['NomeMunicipio'] ?? '';
        $this->uf = $data['UF'] ?? '';
    }

    public function toArray(): array
    {
        return [
            'Cep' => $this->cep,
            'NomeTipoLogradouro' => $this->nomeTipoLogradouro,
            'NomeLogradouro' => $this->nomeLogradouro,
            'Numero' => $this->numero,
            'Complemento' => $this->complemento,
            'NomeBairro' => $this->nomeBairro,
            'NomeMunicipio' => $this->nomeMunicipio,
            'UF' => $this->uf
        ];
    }

    public function getEnderecoCompleto(): string
    {
        $endereco = $this->nomeTipoLogradouro . ' ' . $this->nomeLogradouro;
        
        if ($this->numero) {
            $endereco .= ', ' . $this->numero;
        }
        
        if ($this->complemento) {
            $endereco .= ', ' . $this->complemento;
        }
        
        $endereco .= ', ' . $this->nomeBairro;
        $endereco .= ', ' . $this->nomeMunicipio . ' - ' . $this->uf;
        $endereco .= ', CEP: ' . $this->getCepFormatado();
        
        return $endereco;
    }

    public function getCepFormatado(): string
    {
        if (strlen($this->cep) === 8) {
            return substr($this->cep, 0, 5) . '-' . substr($this->cep, 5, 3);
        }
        return $this->cep;
    }
}
