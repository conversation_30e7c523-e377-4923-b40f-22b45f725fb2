<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Domain\MinisterioSaude\Requests\ObterPrecoMedioItensRequest;
use Domain\MinisterioSaude\Services\ObterPrecoMedioItensService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ObterPrecoMedioItensController extends Controller
{
    protected $precoMedioService;

    public function __construct(ObterPrecoMedioItensService $precoMedioService)
    {
        $this->precoMedioService = $precoMedioService;
    }

    /**
     * Obter preço médio de itens via API do Ministério da Saúde
     *
     * @param ObterPrecoMedioItensRequest $request
     * @return JsonResponse
     */
    public function obterPrecoMedio(ObterPrecoMedioItensRequest $request): JsonResponse
    {
        try {
            Log::info('ObterPrecoMedioItensController - Iniciando consulta de preços médios', [
                'request_data' => $request->validated()
            ]);

            $result = $this->precoMedioService->obterPrecoMedio($request->validated());

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Preços médios consultados com sucesso',
                    'data' => $result['data'],
                    'total_itens' => $result['total_itens'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao consultar preços médios',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('ObterPrecoMedioItensController - Erro não tratado', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Busca preços médios armazenados localmente
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function buscarPrecosLocais(Request $request): JsonResponse
    {
        try {
            Log::info('ObterPrecoMedioItensController - Buscando preços locais', [
                'filters' => $request->all()
            ]);

            $result = $this->precoMedioService->buscarPrecosLocais($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Preços médios locais consultados com sucesso',
                'precos' => $result['precos'],
                'total' => $result['total'],
                'timestamp' => $result['timestamp']
            ], 200);

        } catch (\Exception $e) {
            Log::error('ObterPrecoMedioItensController - Erro ao buscar preços locais', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obter estatísticas de preços por programa
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function obterEstatisticas(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'codigo_programa' => 'required|integer|in:5,24,25,28',
                'ano_referencia' => 'required|integer|digits:4|min:2020|max:2030',
                'mes_referencia' => 'nullable|integer|min:1|max:12'
            ]);

            Log::info('ObterPrecoMedioItensController - Obtendo estatísticas', [
                'codigo_programa' => $request->codigo_programa,
                'ano_referencia' => $request->ano_referencia,
                'mes_referencia' => $request->mes_referencia
            ]);

            $result = $this->precoMedioService->obterEstatisticasPrograma(
                $request->codigo_programa,
                $request->ano_referencia,
                $request->mes_referencia
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Estatísticas obtidas com sucesso',
                    'estatisticas' => $result['estatisticas'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao obter estatísticas',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('ObterPrecoMedioItensController - Erro ao obter estatísticas', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Comparar preços entre períodos
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function compararPrecos(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'codigo_programa' => 'required|integer|in:5,24,25,28',
                'codigo_medicamento' => 'required|string|max:20',
                'ano_atual' => 'required|integer|digits:4|min:2020|max:2030',
                'mes_atual' => 'nullable|integer|min:1|max:12',
                'ano_anterior' => 'required|integer|digits:4|min:2020|max:2030',
                'mes_anterior' => 'nullable|integer|min:1|max:12'
            ]);

            Log::info('ObterPrecoMedioItensController - Comparando preços', [
                'params' => $request->validated()
            ]);

            $result = $this->precoMedioService->compararPrecosPeriodos($request->validated());

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Comparação realizada com sucesso',
                    'comparacao' => $result['comparacao'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao comparar preços',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('ObterPrecoMedioItensController - Erro ao comparar preços', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lista os programas disponíveis
     *
     * @return JsonResponse
     */
    public function listarProgramas(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'programas_disponiveis' => $this->precoMedioService->obterProgramasDisponiveis()
        ], 200);
    }

    /**
     * Retorna informações sobre a API
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'api' => 'API 3.4 - Obter Preço Médio de Itens',
            'description' => 'Consulta preços médios de medicamentos e fármacos dos programas do Ministério da Saúde',
            'version' => '1.0.0',
            'endpoints' => [
                'POST /ministerio-saude/preco-medio-itens/obter' => 'Consultar preços médios na API do MS',
                'GET /ministerio-saude/preco-medio-itens/locais' => 'Buscar preços armazenados localmente',
                'POST /ministerio-saude/preco-medio-itens/estatisticas' => 'Obter estatísticas por programa',
                'POST /ministerio-saude/preco-medio-itens/comparar' => 'Comparar preços entre períodos',
                'GET /ministerio-saude/preco-medio-itens/programas' => 'Listar programas disponíveis',
                'GET /ministerio-saude/preco-medio-itens/info' => 'Informações sobre a API'
            ],
            'required_parameters' => [
                'codigo_programa' => 'integer (5,24,25,28)',
                'ano_referencia' => 'integer (YYYY)',
                'access_token' => 'string (max 40)'
            ],
            'optional_parameters' => [
                'mes_referencia' => 'integer (1-12)',
                'codigo_medicamento' => 'string (max 20)',
                'codigo_farmaco' => 'string (max 20)',
                'estado_origem' => 'string (2 chars)'
            ],
            'programs' => [
                5 => 'Diabetes',
                24 => 'Dose Certa',
                25 => 'Saúde da Mulher',
                28 => 'Arboviroses'
            ],
            'features' => [
                'Consulta de preços médios por programa e período',
                'Filtros por medicamento, fármaco e estado',
                'Estatísticas agregadas por programa',
                'Comparação de preços entre períodos',
                'Histórico de variações de preços'
            ]
        ], 200);
    }
}
