<?php

use Domain\MinisterioSaude\Services\ObterPrecoMedioItensService;

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    $service = new ObterPrecoMedioItensService();
    
    $params = [
        'codigo_programa' => 5,
        'ano_referencia' => 2024,
        'mes_referencia' => 8,
        'estado_origem' => 'SP'
    ];
    
    echo "Testando o serviço de preço médio dos itens...\n";
    $resultado = $service->obterPrecoMedio($params);
    
    echo "Resultado:\n";
    echo json_encode($resultado, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
