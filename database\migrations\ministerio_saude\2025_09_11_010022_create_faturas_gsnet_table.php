<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasGsnetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_gsnet', function (Blueprint $table) {
            $table->id();
            
            // Identificadores principais
            $table->string('numero_fatura', 50)->unique()->comment('Número único da fatura');
            $table->string('codigo_cliente', 50)->comment('Código do cliente');
            $table->string('nome_cliente', 255)->comment('Nome do cliente');
            
            // Dados da fatura
            $table->date('data_emissao')->comment('Data de emissão da fatura');
            $table->date('data_vencimento')->comment('Data de vencimento');
            $table->date('data_pagamento')->nullable()->comment('Data de pagamento');
            
            // Valores
            $table->decimal('valor_bruto', 15, 2)->comment('Valor bruto da fatura');
            $table->decimal('valor_desconto', 15, 2)->default(0)->comment('Valor de desconto aplicado');
            $table->decimal('valor_liquido', 15, 2)->comment('Valor líquido da fatura');
            $table->decimal('valor_pago', 15, 2)->nullable()->comment('Valor efetivamente pago');
            
            // Status da fatura
            $table->tinyInteger('status_fatura')->default(1)->comment('1-Pendente, 2-Paga, 3-Cancelada, 4-Vencida');
            $table->string('forma_pagamento', 50)->nullable()->comment('Forma de pagamento utilizada');
            
            // Informações complementares
            $table->text('observacoes')->nullable()->comment('Observações da fatura');
            $table->string('numero_documento', 100)->nullable()->comment('Número do documento relacionado');
            
            // Controle de comunicação com API
            $table->boolean('sincronizada')->default(false)->comment('Se foi sincronizada com o MS');
            $table->datetime('data_ultima_sincronizacao')->nullable()->comment('Última sincronização');
            $table->longText('dados_originais')->nullable()->comment('Dados originais da API');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices
            $table->index(['codigo_cliente', 'status_fatura']);
            $table->index(['data_emissao', 'status_fatura']);
            $table->index('data_vencimento');
            $table->index('sincronizada');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_gsnet');
    }
}
