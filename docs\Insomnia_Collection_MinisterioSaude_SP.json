{"_type": "export", "__export_format": 4, "__export_date": "2025-09-11T18:35:00.000Z", "__export_source": "insomnia.desktop.app:v8.6.1", "resources": [{"_id": "req_ministerio_saude_sp_base", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456400000, "created": 1694456400000, "url": "{{ _.base_url }}/api/ministerio-saude-test", "name": "🔧 Test Connection", "description": "Testar conexão com a API do Ministério da Saúde SP", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "description": "", "id": "pair_auth_test"}], "authentication": {}, "metaSortKey": -1694456400000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_criar_status", "parentId": "fld_status_apis", "modified": 1694456500000, "created": 1694456500000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/status/criar", "name": "1.1 - Criar Status Fatura", "description": "API para criar status de fatura no sistema", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"id_gestor\": \"12345678901234567890\",\n  \"numero_fatura\": \"FAT-{{ _.timestamp }}-001\",\n  \"status_fatura\": 1,\n  \"data_fatura\": \"2024-09-11\",\n  \"valor_total\": 1500.75,\n  \"codigo_programa\": 5,\n  \"access_token\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456500000, "isPrivate": false, "_type": "request"}, {"_id": "req_criar_status_original", "parentId": "fld_status_apis", "modified": 1694456501000, "created": 1694456501000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/status/criar", "name": "1.1 - Criar Status (Formato Original)", "description": "API para criar status usando formato original", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"IdOrigem\": \"{{ _.timestamp }}\",\n    \"NomeStatus\": \"Aprovado\",\n    \"DescricaoStatus\": \"Fatura aprovada pelo sistema de validacao\"\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456501000, "isPrivate": false, "_type": "request"}, {"_id": "req_consultar_status", "parentId": "fld_status_apis", "modified": 1694456600000, "created": 1694456600000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/status/consultar", "name": "1.2 - Consultar Status", "description": "API para consultar status cadastrados", "method": "GET", "body": {}, "parameters": [{"name": "AccessToken", "value": "{{ _.access_token }}", "description": "Token de acesso obrigatório", "id": "pair_access_token", "disabled": false}, {"name": "Id<PERSON><PERSON><PERSON>", "value": "12345", "description": "Filtrar por ID de origem (opcional)", "id": "pair_id_origem", "disabled": true}, {"name": "NomeStatus", "value": "FAT-2024-001", "description": "Filtrar por nome/número da fatura (opcional)", "id": "pair_nome_status", "disabled": true}, {"name": "IdStatus", "value": "21", "description": "Filtrar por ID específico (opcional)", "id": "pair_id_status", "disabled": true}], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456600000, "isPrivate": false, "_type": "request"}, {"_id": "req_listar_operadores", "parentId": "fld_operadores_apis", "modified": 1694456700000, "created": 1694456700000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/operadores", "name": "1.3 - Listar Operadores", "description": "API para listar operadores disponíveis", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456700000, "isPrivate": false, "_type": "request"}, {"_id": "req_consultar_detalhes_fatura", "parentId": "fld_faturas_apis", "modified": 1694456800000, "created": 1694456800000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas/123456789/detalhes", "name": "2.2 - Consultar <PERSON><PERSON><PERSON>", "description": "API para consultar detalhes de uma fatura específica", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456800000, "isPrivate": false, "_type": "request"}, {"_id": "req_consultar_itens_gsnet", "parentId": "fld_itens_apis", "modified": 1694456900000, "created": 1694456900000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/itens/consultar", "name": "1.5 - Consultar Itens GSNET", "description": "API para consultar itens do sistema GSNET", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"ListaItens\": [\n      {\n        \"CodigoMaterial\": 1560\n      },\n      {\n        \"CodigoMaterial\": 785\n      },\n      {\n        \"CodigoMaterial\": 999\n      }\n    ]\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694456900000, "isPrivate": false, "_type": "request"}, {"_id": "req_consultar_faturas_farmanet", "parentId": "fld_farmanet_apis", "modified": 1694457000000, "created": 1694457000000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas-farmanet/consultar", "name": "3.1 - Consultar Faturas <PERSON>", "description": "API para consultar faturas do sistema Farmanet", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"CodigoPrograma\": 5,\n    \"DataInicio\": \"2024-01-01\",\n    \"DataFim\": \"2024-12-31\",\n    \"StatusFatura\": \"A\"\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694457000000, "isPrivate": false, "_type": "request"}, {"_id": "req_confirmar_recebimento_farmanet", "parentId": "fld_farmanet_apis", "modified": 1694457100000, "created": 1694457100000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/faturas-farmanet/confirmar-recebimento", "name": "3.2 - <PERSON><PERSON><PERSON><PERSON>anet", "description": "API para confirmar recebimento de faturas Farmanet", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"IdFatura\": \"123456789\",\n    \"StatusRecebimento\": \"CONFIRMADO\",\n    \"DataRecebimento\": \"2024-09-11T15:30:00\",\n    \"ObservacaoRecebimento\": \"Fatura recebida e processada com sucesso\"\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694457100000, "isPrivate": false, "_type": "request"}, {"_id": "req_consultar_pedidos_farmanet", "parentId": "fld_farmanet_pedidos_apis", "modified": 1694457200000, "created": 1694457200000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/pedidos-farmanet/consultar", "name": "3.3 - Consultar Pedidos Farmanet", "description": "API para consultar pedidos do sistema Farmanet", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"CodigoPrograma\": 5,\n    \"DataInicio\": \"2024-01-01\",\n    \"DataFim\": \"2024-12-31\",\n    \"StatusPedido\": \"PENDENTE\"\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694457200000, "isPrivate": false, "_type": "request"}, {"_id": "req_atualizar_pedido_farmanet", "parentId": "fld_farmanet_pedidos_apis", "modified": 1694457300000, "created": 1694457300000, "url": "{{ _.base_url }}/api/ministerio-saude/sp/pedidos-farmanet/atualizar", "name": "3.4 - <PERSON><PERSON><PERSON><PERSON> Pedido <PERSON>", "description": "API para atualizar status de pedidos Farmanet", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"Data\": {\n    \"IdPedido\": \"PED-123456789\",\n    \"NovoStatus\": \"PROCESSANDO\",\n    \"DataAtualizacao\": \"2024-09-11T15:30:00\",\n    \"ObservacaoAtualizacao\": \"Pedido em processamento pelo sistema\"\n  },\n  \"AccessToken\": \"{{ _.access_token }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json", "id": "pair_content_type"}, {"name": "Authorization", "value": "Bearer {{ _.jwt_token }}", "id": "pair_auth"}], "authentication": {}, "metaSortKey": -1694457300000, "isPrivate": false, "_type": "request"}, {"_id": "wrk_ministerio_saude_sp", "parentId": null, "modified": 1694456000000, "created": 1694456000000, "name": "Ministério da Saúde SP - APIs", "description": "Collection completa das APIs do Ministério da Saúde de São Paulo", "scope": "collection", "_type": "workspace"}, {"_id": "fld_status_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456100000, "created": 1694456100000, "name": "📊 Status APIs", "description": "APIs relacionadas ao gerenciamento de status", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456100000, "_type": "request_group"}, {"_id": "fld_operadores_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456200000, "created": 1694456200000, "name": "👥 Operadores APIs", "description": "APIs relacionadas aos operadores do sistema", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456200000, "_type": "request_group"}, {"_id": "fld_faturas_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456300000, "created": 1694456300000, "name": "💰 Faturas APIs", "description": "APIs relacionadas ao gerenciamento de faturas", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456300000, "_type": "request_group"}, {"_id": "fld_itens_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456400000, "created": 1694456400000, "name": "📦 Itens APIs", "description": "APIs relacionadas aos itens e materiais GSNET", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456400000, "_type": "request_group"}, {"_id": "fld_farmanet_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456500000, "created": 1694456500000, "name": "💊 Farmanet - Fat<PERSON><PERSON>", "description": "APIs do sistema Farmanet para faturas", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456500000, "_type": "request_group"}, {"_id": "fld_farmanet_pedidos_apis", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456600000, "created": 1694456600000, "name": "💊 Farmanet - Pedidos", "description": "APIs do sistema Farmanet para pedidos", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1694456600000, "_type": "request_group"}, {"_id": "env_base", "parentId": "wrk_ministerio_saude_sp", "modified": 1694456000000, "created": 1694456000000, "name": "Base Environment", "data": {"base_url": "http://localhost:8000", "jwt_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjdXN0b21lcl9pZCI6MTMsInVzZXJfaWQiOjIsImNucGoiOiI4MTcyNzQxNDAwMDk3NiIsImlhdCI6MTY4MDI3OTk2OCwiZXhwIjpudWxsfQ.-kwFgFDxHTjuwsXOES2Ava4M-x-EWZ_RxN_nLlGrWCE", "access_token": "7fe9ba49-6ce4-4773-b7a6-f6e46a12565b", "timestamp": "{{ moment.unix() }}"}, "dataPropertyOrder": {"&": ["base_url", "jwt_token", "access_token", "timestamp"]}, "color": null, "isPrivate": false, "metaSortKey": 1, "_type": "environment"}, {"_id": "env_production", "parentId": "env_base", "modified": 1694456001000, "created": 1694456001000, "name": "Production", "data": {"base_url": "https://web.ibllogistica.com.br/docs"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": "#ff6b6b", "isPrivate": false, "metaSortKey": 2, "_type": "environment"}, {"_id": "env_homolog", "parentId": "env_base", "modified": 1694456002000, "created": 1694456002000, "name": "Homolog", "data": {"base_url": "https://web.ibllogistica.com.br/docs-homolog"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": "#4ecdc4", "isPrivate": false, "metaSortKey": 3, "_type": "environment"}]}