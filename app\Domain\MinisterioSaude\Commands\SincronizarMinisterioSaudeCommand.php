<?php

namespace Domain\MinisterioSaude\Commands;

use Illuminate\Console\Command;
use Domain\MinisterioSaude\Services\EnderecoLocalService;
use Domain\MinisterioSaude\Services\StatusOperadorService;

class SincronizarMinisterioSaudeCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ministerio-saude:sincronizar 
                           {--tipo=all : Tipo de sincronização (status, enderecos, all)}
                           {--limite=50 : Limite de registros para processamento}
                           {--geolocalizacao : Processar geolocalização}';

    /**
     * The console command description.
     */
    protected $description = 'Sincronizar dados com a API do Ministério da Saúde SP';

    private EnderecoLocalService $enderecoService;
    private StatusOperadorService $statusService;

    public function __construct(
        EnderecoLocalService $enderecoService,
        StatusOperadorService $statusService
    ) {
        parent::__construct();
        $this->enderecoService = $enderecoService;
        $this->statusService = $statusService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $tipo = $this->option('tipo');
        $limite = (int) $this->option('limite');
        $processarGeo = $this->option('geolocalizacao');

        $this->info('=== Sincronização Ministério da Saúde SP ===');
        $this->info('Tipo: ' . $tipo);
        $this->info('Limite: ' . $limite);
        $this->info('Geolocalização: ' . ($processarGeo ? 'Sim' : 'Não'));
        $this->newLine();

        $sucesso = true;

        // Sincronizar status
        if (in_array($tipo, ['all', 'status'])) {
            $this->info('📊 Sincronizando status...');
            $resultadoStatus = $this->sincronizarStatus();
            if (!$resultadoStatus) {
                $sucesso = false;
            }
            $this->newLine();
        }

        // Processar geolocalização
        if ($processarGeo || in_array($tipo, ['all', 'enderecos'])) {
            $this->info('🌍 Processando geolocalização...');
            $resultadoGeo = $this->processarGeolocalizacao($limite);
            if (!$resultadoGeo) {
                $sucesso = false;
            }
            $this->newLine();
        }

        // Mostrar estatísticas finais
        $this->mostrarEstatisticas();

        if ($sucesso) {
            $this->info('✅ Sincronização concluída com sucesso!');
            return Command::SUCCESS;
        } else {
            $this->error('❌ Sincronização concluída com erros. Verifique os logs.');
            return Command::FAILURE;
        }
    }

    private function sincronizarStatus(): bool
    {
        try {
            // Implementar sincronização de status se necessário
            $this->line('Status já sincronizado via consulta automática.');
            return true;
        } catch (\Exception $e) {
            $this->error('Erro ao sincronizar status: ' . $e->getMessage());
            return false;
        }
    }

    private function processarGeolocalizacao(int $limite): bool
    {
        try {
            if (!config('ministerio_saude.geolocalizacao.enabled')) {
                $this->warn('⚠️  Geolocalização está desabilitada nas configurações.');
                return true;
            }

            $resultado = $this->enderecoService->processarGeolocalizacaoLote($limite);

            if ($resultado['success']) {
                $dados = $resultado['data'];
                
                $this->info("✅ Processados: {$dados['processados']}");
                $this->info("✅ Sucessos: {$dados['sucessos']}");
                $this->error("❌ Erros: {$dados['erros']}");

                // Mostrar progresso detalhado se solicitado
                if ($this->output->isVerbose()) {
                    $this->table(
                        ['ID', 'Status', 'Distância (km)'],
                        array_map(function($item) {
                            return [
                                $item['id'],
                                $item['status'],
                                $item['distancia_km'] ?? 'N/A'
                            ];
                        }, $dados['detalhes'])
                    );
                }

                return true;
            } else {
                $this->error('Erro ao processar geolocalização: ' . $resultado['message']);
                return false;
            }

        } catch (\Exception $e) {
            $this->error('Erro ao processar geolocalização: ' . $e->getMessage());
            return false;
        }
    }

    private function mostrarEstatisticas(): void
    {
        try {
            $this->info('📈 Estatísticas finais:');
            
            $stats = $this->enderecoService->obterEstatisticas();
            
            if ($stats['success']) {
                $dados = $stats['data'];
                
                $this->table(
                    ['Métrica', 'Valor'],
                    [
                        ['Total de Endereços', $dados['total_enderecos']],
                        ['Endereços Ativos', $dados['enderecos_ativos']],
                        ['Com Geolocalização', $dados['com_geolocalizacao']],
                        ['Sem Geolocalização', $dados['sem_geolocalizacao']],
                        ['% Geolocalizados', $dados['percentual_geolocalizados'] . '%']
                    ]
                );
            }

        } catch (\Exception $e) {
            $this->warn('Não foi possível obter estatísticas: ' . $e->getMessage());
        }
    }
}
