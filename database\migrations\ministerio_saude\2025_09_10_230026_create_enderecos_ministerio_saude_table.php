<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEnderecosMinisterioSaudeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('enderecos_ministerio_saude', function (Blueprint $table) {
            $table->id();
            
            // Identificadores
            $table->string('codigo_endereco', 50)->unique()->comment('Código único do endereço');
            $table->string('tipo_endereco', 50)->comment('Tipo do endereço (sede, filial, etc)');
            
            // Dados do endereço
            $table->string('logradouro', 255)->comment('Logradouro');
            $table->string('numero', 10)->nullable()->comment('Número');
            $table->string('complemento', 100)->nullable()->comment('Complemento');
            $table->string('bairro', 100)->comment('Bairro');
            $table->string('cidade', 100)->comment('Cidade');
            $table->string('uf', 2)->comment('Estado (UF)');
            $table->string('cep', 10)->comment('CEP');
            
            // Coordenadas geográficas
            $table->decimal('latitude', 10, 8)->nullable()->comment('Latitude');
            $table->decimal('longitude', 11, 8)->nullable()->comment('Longitude');
            
            // Informações de contato
            $table->string('telefone', 20)->nullable()->comment('Telefone');
            $table->string('email', 255)->nullable()->comment('E-mail');
            
            // Status e observações
            $table->tinyInteger('ativo')->default(1)->comment('1-Ativo, 0-Inativo');
            $table->text('observacoes')->nullable()->comment('Observações gerais');
            
            // Dados da API
            $table->longText('dados_originais')->nullable()->comment('Dados originais da resposta da API');
            
            // Controle de datas
            $table->datetime('data_consulta')->nullable()->comment('Data da consulta na API');
            $table->datetime('data_ultima_atualizacao')->nullable()->comment('Última atualização dos dados');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices
            $table->index(['uf', 'cidade']);
            $table->index(['tipo_endereco', 'ativo']);
            $table->index('cep');
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('enderecos_ministerio_saude');
    }
}
