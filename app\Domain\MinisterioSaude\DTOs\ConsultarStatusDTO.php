<?php

namespace Domain\MinisterioSaude\DTOs;

class ConsultarStatusDTO
{
    public string $accessToken;
    public ?string $idStatus;
    public ?string $idOrigem;
    public ?string $nomeStatus;

    public function __construct(
        string $accessToken,
        ?string $idStatus = null,
        ?string $idOrigem = null,
        ?string $nomeStatus = null
    ) {
        $this->accessToken = $accessToken;
        $this->idStatus = $idStatus;
        $this->idOrigem = $idOrigem;
        $this->nomeStatus = $nomeStatus;
    }

    public function toQueryParams(): array
    {
        $params = [
            'AccessToken' => $this->accessToken
        ];

        if ($this->idStatus) {
            $params['IdStatus'] = $this->idStatus;
        }

        if ($this->idOrigem) {
            $params['IdOrigem'] = $this->idOrigem;
        }

        if ($this->nomeStatus) {
            $params['NomeStatus'] = $this->nomeStatus;
        }

        return $params;
    }

    public function validate(): array
    {
        $errors = [];

        if (empty($this->accessToken)) {
            $errors[] = 'AccessToken não informado';
        } elseif (strlen($this->accessToken) > 240) {
            $errors[] = 'AccessToken deve ter no máximo 240 caracteres';
        }

        if ($this->idStatus && strlen($this->idStatus) > 40) {
            $errors[] = 'IdStatus deve ter no máximo 40 caracteres';
        }

        if ($this->idOrigem && strlen($this->idOrigem) > 40) {
            $errors[] = 'IdOrigem deve ter no máximo 40 caracteres';
        }

        if ($this->nomeStatus && strlen($this->nomeStatus) > 40) {
            $errors[] = 'NomeStatus deve ter no máximo 40 caracteres';
        }

        return $errors;
    }

    public function isValid(): bool
    {
        return empty($this->validate());
    }
}
