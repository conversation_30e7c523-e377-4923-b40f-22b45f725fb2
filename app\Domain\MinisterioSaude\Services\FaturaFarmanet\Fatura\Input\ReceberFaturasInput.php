<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;

class ReceberFaturasInput
{
    public ?string $dtDocumento;
    public ?string $nrDocumento;
    public ?string $vlDocumento;
    public ?string $idProgramaSaude;
    public ?string $idLocalDestino;
    public ?string $cdPedido;
    /** @var ?ItemReceberInput[] */
    public ?array $itens;
    public ?string $systemCode;

    public function __construct(
        ?string $dtDocumento,
        ?string $nrDocumento,
        ?string $vlDocumento,
        ?string $idProgramaSaude,
        ?string $idLocalDestino,
        ?string $cdPedido,
        ?array $itens,
        ?string $systemCode
    ) {
        $this->dtDocumento = $dtDocumento;
        $this->nrDocumento = $nrDocumento;
        $this->vlDocumento = $vlDocumento;
        $this->idProgramaSaude = $idProgramaSaude;
        $this->idLocalDestino = $idLocalDestino;
        $this->cdPedido = $cdPedido;
        $this->itens = $itens;
        $this->systemCode = $systemCode;
    }

    public static function fromArray(array $data): ReceberFaturasInput
    {
        $itens = null;
        if (isset($data['itens']) && is_array($data['itens'])) {
            $itens = [];
            foreach ($data['itens'] as $item) {
                $itens[] = ItemReceberInput::fromArray($item);
            }
        }
        return new self(
            $data['dt_documento'] ?? null,
            $data['nr_documento'] ?? null,
            $data['vl_documento'] ?? null,
            $data['id_programa_saude'] ?? null,
            $data['id_local_destino'] ?? null,
            $data['cd_pedido'] ?? null,
            $itens,
            $data['system_code'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'dt_documento' => $this->dtDocumento,
            'nr_documento' => $this->nrDocumento,
            'vl_documento' => $this->vlDocumento,
            'id_programa_saude' => $this->idProgramaSaude,
            'id_local_destino' => $this->idLocalDestino,
            'cd_pedido' => $this->cdPedido,
            'itens' => $this->itens,
            'system_code' => $this->systemCode
        ];
    }

    /**
     * Converte para o formato JSON esperado pela API
     *
     * @return array
     */
    public function toQueryParams(string $accessToken): array
    {
        $itensArray = [];

        foreach ($this->itens ?? [] as $item) {
            $itensArray[] = $item->toQueryParams();
        }

        return [
            'Data' => [
                'DtDocumento' => $this->dtDocumento,
                'NrDocumento' => $this->nrDocumento,
                'VlDocumento' => $this->vlDocumento,
                'IdProgramaSaude' => $this->idProgramaSaude,
                'IdLocalDestino' => $this->idLocalDestino,
                'cdPedido' => $this->cdPedido,
                'Itens' => $itensArray
            ],
            'AccessToken' => $accessToken,
            'SystemCode' => $this->systemCode
        ];
    }
}


class ItemReceberInput
{
    public ?string $codigoItem;
    public ?string $quantidade;

    public function __construct(?string $codigoItem, ?string $quantidade)
    {
        $this->codigoItem = $codigoItem;
        $this->quantidade = $quantidade;
    }

    public static function fromArray(array $data): self
    {
        return new self($data['codigo_item'] ?? null, $data['quantidade'] ?? null);
    }

    public function toQueryParams(): array
    {
        return [
            'CodigoItem' => $this->codigoItem,
            'Quantidade' => $this->quantidade
        ];
    }

    public function toArray(): array
    {
        return [
            'codigo_item' => $this->codigoItem,
            'quantidade' => $this->quantidade
        ];
    }
}
