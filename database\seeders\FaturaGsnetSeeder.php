<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FaturaGsnet;
use App\Models\FaturaGsnetItem;
use App\Models\FaturaGsnetStatusControle;
use Carbon\Carbon;

class FaturaGsnetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Fatura 1
        $fatura1 = FaturaGsnet::create([
            'protocolo_id_gsnet' => 'PROT-2025-001',
            'id_gestor' => 'GESTOR001',
            'nr_documento' => 'DOC001',
            'descricao_documento' => 'Fatura de medicamentos básicos',
            'nr_processo' => 'PROC001',
            'descricao_processo' => 'Aquisição de medicamentos para UBS',
            'valor_total' => 15000.00,
            'local_origem_id' => 1,
            'local_origem_codigo' => 'UBS001',
            'local_origem_descricao' => 'UBS Centro - São Paulo',
            'local_destino_id' => 2,
            'local_destino_codigo' => 'FARM001',
            'local_destino_descricao' => 'Farmácia Central - São Paulo',
            'status_atual' => 'ATIVO',
            'data_criacao' => Carbon::now()->subDays(5),
            'data_ultima_atualizacao' => Carbon::now()->subDays(2),
            'ativo' => true
        ]);

        // Itens da Fatura 1
        FaturaGsnetItem::create([
            'fatura_id' => $fatura1->id,
            'protocolo_id_gsnet' => $fatura1->protocolo_id_gsnet,
            'codigo_material' => 1001,
            'codigo_siafisico' => 2001,
            'nome_material' => 'Dipirona 500mg - Caixa com 20 comprimidos',
            'quantidade_material' => 100.000,
            'preco_medio' => 12.5000000,
            'ativo' => true
        ]);

        FaturaGsnetItem::create([
            'fatura_id' => $fatura1->id,
            'protocolo_id_gsnet' => $fatura1->protocolo_id_gsnet,
            'codigo_material' => 1002,
            'codigo_siafisico' => 2002,
            'nome_material' => 'Paracetamol 750mg - Caixa com 20 comprimidos',
            'quantidade_material' => 80.000,
            'preco_medio' => 15.7500000,
            'ativo' => true
        ]);

        // Status da Fatura 1
        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura1->id,
            'protocolo_id_gsnet' => $fatura1->protocolo_id_gsnet,
            'status_codigo' => 'CRIADO',
            'status_descricao' => 'Fatura criada no sistema',
            'observacao' => 'Fatura inicial criada automaticamente',
            'data_status' => Carbon::now()->subDays(5),
            'enviado_ministerio' => false
        ]);

        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura1->id,
            'protocolo_id_gsnet' => $fatura1->protocolo_id_gsnet,
            'status_codigo' => 'ATIVO',
            'status_descricao' => 'Fatura ativada para processamento',
            'observacao' => 'Aprovada para processamento pela equipe técnica',
            'data_status' => Carbon::now()->subDays(2),
            'enviado_ministerio' => false
        ]);

        // Fatura 2
        $fatura2 = FaturaGsnet::create([
            'protocolo_id_gsnet' => 'PROT-2025-002',
            'id_gestor' => 'GESTOR001',
            'nr_documento' => 'DOC002',
            'descricao_documento' => 'Fatura de material hospitalar',
            'nr_processo' => 'PROC002',
            'descricao_processo' => 'Aquisição de material para hospital',
            'valor_total' => 25000.00,
            'local_origem_id' => 3,
            'local_origem_codigo' => 'HOSP001',
            'local_origem_descricao' => 'Hospital das Clínicas - São Paulo',
            'local_destino_id' => 4,
            'local_destino_codigo' => 'DIST001',
            'local_destino_descricao' => 'Distribuidora Médica Central',
            'status_atual' => 'PROC',
            'data_criacao' => Carbon::now()->subDays(3),
            'data_ultima_atualizacao' => Carbon::now()->subDay(),
            'ativo' => true
        ]);

        // Itens da Fatura 2
        FaturaGsnetItem::create([
            'fatura_id' => $fatura2->id,
            'protocolo_id_gsnet' => $fatura2->protocolo_id_gsnet,
            'codigo_material' => 2001,
            'codigo_siafisico' => 3001,
            'nome_material' => 'Seringa descartável 10ml - Pacote com 100 unidades',
            'quantidade_material' => 50.000,
            'preco_medio' => 45.0000000,
            'ativo' => true
        ]);

        FaturaGsnetItem::create([
            'fatura_id' => $fatura2->id,
            'protocolo_id_gsnet' => $fatura2->protocolo_id_gsnet,
            'codigo_material' => 2002,
            'codigo_siafisico' => 3002,
            'nome_material' => 'Luva cirúrgica estéril - Caixa com 50 pares',
            'quantidade_material' => 30.000,
            'preco_medio' => 85.0000000,
            'ativo' => true
        ]);

        // Status da Fatura 2
        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura2->id,
            'protocolo_id_gsnet' => $fatura2->protocolo_id_gsnet,
            'status_codigo' => 'CRIADO',
            'status_descricao' => 'Fatura criada no sistema',
            'observacao' => 'Fatura inicial criada automaticamente',
            'data_status' => Carbon::now()->subDays(3),
            'enviado_ministerio' => false
        ]);

        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura2->id,
            'protocolo_id_gsnet' => $fatura2->protocolo_id_gsnet,
            'status_codigo' => 'PROC',
            'status_descricao' => 'Em processamento',
            'observacao' => 'Fatura em análise pela equipe de compras',
            'data_status' => Carbon::now()->subDay(),
            'enviado_ministerio' => false
        ]);

        // Fatura 3 - Outro gestor
        $fatura3 = FaturaGsnet::create([
            'protocolo_id_gsnet' => 'PROT-2025-003',
            'id_gestor' => 'GESTOR002',
            'nr_documento' => 'DOC003',
            'descricao_documento' => 'Fatura de equipamentos médicos',
            'nr_processo' => 'PROC003',
            'descricao_processo' => 'Aquisição de equipamentos para UTI',
            'valor_total' => 125000.00,
            'local_origem_id' => 5,
            'local_origem_codigo' => 'UTI001',
            'local_origem_descricao' => 'UTI Hospital São João',
            'local_destino_id' => 6,
            'local_destino_codigo' => 'FORNEC001',
            'local_destino_descricao' => 'Fornecedor Equipamentos Médicos Ltda',
            'status_atual' => 'FINALIZADO',
            'data_criacao' => Carbon::now()->subDays(10),
            'data_ultima_atualizacao' => Carbon::now()->subDays(1),
            'ativo' => true
        ]);

        // Itens da Fatura 3
        FaturaGsnetItem::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'codigo_material' => 3001,
            'codigo_siafisico' => 4001,
            'nome_material' => 'Monitor cardíaco digital',
            'quantidade_material' => 2.000,
            'preco_medio' => 45000.0000000,
            'ativo' => true
        ]);

        FaturaGsnetItem::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'codigo_material' => 3002,
            'codigo_siafisico' => 4002,
            'nome_material' => 'Ventilador pulmonar portátil',
            'quantidade_material' => 1.000,
            'preco_medio' => 35000.0000000,
            'ativo' => true
        ]);

        // Status da Fatura 3
        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'status_codigo' => 'CRIADO',
            'status_descricao' => 'Fatura criada no sistema',
            'observacao' => 'Fatura inicial criada automaticamente',
            'data_status' => Carbon::now()->subDays(10),
            'enviado_ministerio' => false
        ]);

        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'status_codigo' => 'ATIVO',
            'status_descricao' => 'Fatura ativada para processamento',
            'observacao' => 'Aprovada para processamento',
            'data_status' => Carbon::now()->subDays(8),
            'enviado_ministerio' => false
        ]);

        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'status_codigo' => 'PROC',
            'status_descricao' => 'Em processamento',
            'observacao' => 'Fatura processada pela equipe de compras',
            'data_status' => Carbon::now()->subDays(5),
            'enviado_ministerio' => false
        ]);

        FaturaGsnetStatusControle::create([
            'fatura_id' => $fatura3->id,
            'protocolo_id_gsnet' => $fatura3->protocolo_id_gsnet,
            'status_codigo' => 'FINALIZADO',
            'status_descricao' => 'Fatura finalizada',
            'observacao' => 'Processo concluído com sucesso',
            'data_status' => Carbon::now()->subDay(),
            'enviado_ministerio' => true,
            'data_envio' => Carbon::now()->subDay(),
            'resposta_ministerio' => 'Fatura processada com sucesso pelo Ministério da Saúde'
        ]);

        $this->command->info('✅ Faturas GSNET criadas com sucesso!');
        $this->command->info('📊 Total: 3 faturas, 6 itens, 8 registros de status');
    }
}
