<?php

namespace Domain\MinisterioSaude\DTOs;

class ConsultarEnderecoDTO
{
    public string $accessToken;
    public string $idGestor;
    public string $idLocal;

    public function __construct(
        string $accessToken,
        string $idGestor,
        string $idLocal
    ) {
        $this->accessToken = $accessToken;
        $this->idGestor = $idGestor;
        $this->idLocal = $idLocal;
    }

    public function toQueryParams(): array
    {
        return [
            'IdGestor' => $this->idGestor,
            'IdLocal' => $this->idLocal,
            'AccessToken' => $this->accessToken
        ];
    }

    public function validate(): array
    {
        $errors = [];

        if (empty($this->idGestor)) {
            $errors[] = 'IdGestor não informado';
        } elseif (strlen($this->idGestor) > 4) {
            $errors[] = 'IdGestor deve ter no máximo 4 caracteres';
        }

        if (empty($this->idLocal)) {
            $errors[] = 'IdLocal não informado';
        } elseif (strlen($this->idLocal) > 5) {
            $errors[] = 'IdLocal deve ter no máximo 5 caracteres';
        }

        if (empty($this->accessToken)) {
            $errors[] = 'AccessToken não informado';
        } elseif (strlen($this->accessToken) > 240) {
            $errors[] = 'AccessToken deve ter no máximo 240 caracteres';
        }

        return $errors;
    }

    public function isValid(): bool
    {
        return empty($this->validate());
    }
}
