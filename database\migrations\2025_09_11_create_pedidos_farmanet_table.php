<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePedidosFarmanetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('pedidos_farmanet', function (Blueprint $table) {
            $table->id();
            $table->string('protocolo_id_gsnet', 40)->unique()->comment('Protocolo único do GSNET');
            $table->integer('cd_programa')->comment('Código do programa: 28=Arboviroses, 24=Dose Certa, 25=<PERSON><PERSON><PERSON> Mu<PERSON>her, 5=Diabetes');
            $table->bigInteger('nr_pedido')->comment('Número do pedido sequencial do Farmanet');
            $table->integer('cd_periodicidade')->nullable()->comment('1=Mensal, 2=Bimestral, 3=Trimestral, etc');
            $table->integer('ano_periodo')->nullable()->comment('Ano do período de atendimento');
            $table->integer('nr_periodo')->nullable()->comment('Número do período conforme periodicidade');
            $table->integer('id_local')->nullable()->comment('Código do local no GSNET');
            $table->string('id_gestor', 22)->comment('ID do gestor');
            $table->integer('ano_referencia')->comment('Ano de referência com 4 dígitos');
            $table->integer('mes_referencia')->nullable()->comment('Mês de referência com 2 dígitos');
            $table->integer('ano_periodo_referencia')->nullable()->comment('Ano período referência');
            $table->boolean('consumido')->default(false)->comment('Se o pedido já foi consumido/atualizado');
            $table->string('protocolo_operador', 50)->nullable()->comment('Protocolo do operador logístico');
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['cd_programa', 'ano_referencia', 'mes_referencia']);
            $table->index(['id_gestor']);
            $table->index(['protocolo_id_gsnet']);
            $table->index(['consumido']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('pedidos_farmanet');
    }
}
