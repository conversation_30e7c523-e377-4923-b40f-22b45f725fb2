<?php

namespace Domain\MinisterioSaude\Services\Planejamento\Input;

class AtualizarPedidoInput
{
    public int $idProgramaSaude;
    public int $codigoPedido;
    public string $protocoloOperador;

    public function __construct(int $idProgramaSaude, int $codigoPedido, string $protocoloOperador)
    {
        $this->idProgramaSaude = $idProgramaSaude;
        $this->codigoPedido = $codigoPedido;
        $this->protocoloOperador = $protocoloOperador;
    }

    public static function fromArray(array $data): AtualizarPedidoInput
    {
        return new self(
            $data['id_programa_saude'],
            $data['codigo_pedido'],
            $data['protocolo_operador']
        );
    }

    public function toArray(): array
    {
        return [
            'id_programa_saude' => $this->idProgramaSaude,
            'codigo_pedido' => $this->codigoPedido,
            'protocolo_operador' => $this->protocoloOperador,
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'IdProgramaSaude' => $this->idProgramaSaude,
            'CodigoPedido' => $this->codigoPedido,
            'ProtocoloOperador' => $this->protocoloOperador,
            'AccessToken' => $accessToken
        ];
    }
}
