<?php

namespace Domain\MinisterioSaude\Traits;

use Illuminate\Support\Facades\Log;
use Throwable;

trait HasLog
{
    protected function logInfo(string $message, array $context): void
    {
        Log::info($message, $context);
    }

    protected function logError(string $message, Throwable $exception, array $context = []): void
    {
        Log::error($message, array_merge($context, [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]));
    }

    protected function logWarning(string $message, array $context): void
    {
        Log::warning($message, $context);
    }
}
