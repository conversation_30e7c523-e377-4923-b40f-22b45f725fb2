<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasFarmanetItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_farmanet_itens', function (Blueprint $table) {
            $table->id();
            
            // Relacionamento com fatura
            $table->foreignId('fatura_farmanet_id')->constrained('faturas_farmanet')->onDelete('cascade');
            
            // Identificação do medicamento
            $table->string('codigo_medicamento', 20)->nullable()->comment('Código do medicamento');
            $table->string('nome_medicamento', 255)->nullable()->comment('Nome do medicamento');
            $table->string('codigo_farmaco', 20)->nullable()->comment('Código do fármaco');
            
            // Quantidade e valores
            $table->integer('quantidade')->default(0)->comment('Quantidade faturada');
            $table->decimal('valor_unitario', 10, 4)->nullable()->comment('Valor unitário');
            $table->decimal('valor_total', 15, 2)->nullable()->comment('Valor total do item');
            $table->decimal('desconto_aplicado', 10, 4)->nullable()->comment('Desconto aplicado');
            
            // Observações e dados originais
            $table->text('observacoes')->nullable()->comment('Observações do item');
            $table->longText('dados_originais')->nullable()->comment('JSON com dados originais da API');
            
            // Controle interno
            $table->timestamps();
            
            // Índices
            $table->index(['codigo_medicamento', 'codigo_farmaco']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_farmanet_itens');
    }
}
