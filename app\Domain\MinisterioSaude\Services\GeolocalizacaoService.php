<?php

namespace Domain\MinisterioSaude\Services;

use Domain\MinisterioSaude\Models\EnderecoLocal;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GeolocalizacaoService
{
    private const ENDERECO_BASE = 'Rua Man<PERSON>l Borba Gato, 100, São Paulo, SP';
    private const COORDENADAS_BASE_LAT = -23.5889; // Exemplo, ajustar conforme necessário
    private const COORDENADAS_BASE_LON = -46.6418; // Exemplo, ajustar conforme necessário
    
    private ?string $distanceMatrixApiKey;
    private string $distanceMatrixBaseUrl = 'https://api.distancematrix.ai';

    public function __construct()
    {
        $this->distanceMatrixApiKey = config('services.distance_matrix.api_key', '');
    }

    /**
     * Obter coordenadas de um endereço usando a API DistanceMatrix
     */
    public function obterCoordenadas(string $endereco): ?array
    {
        try {
            $cacheKey = 'geo_coords_' . md5($endereco);
            
            return Cache::remember($cacheKey, 86400, function () use ($endereco) {
                $response = Http::timeout(30)->get($this->distanceMatrixBaseUrl . '/maps/api/geocode/json', [
                    'address' => $endereco,
                    'key' => $this->distanceMatrixApiKey
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    
                    if ($data['status'] === 'OK' && !empty($data['results'])) {
                        $location = $data['results'][0]['geometry']['location'];
                        
                        return [
                            'latitude' => $location['lat'],
                            'longitude' => $location['lng']
                        ];
                    }
                }

                Log::warning('GeolocalizacaoService: Não foi possível obter coordenadas', [
                    'endereco' => $endereco,
                    'response' => $response->json()
                ]);

                return null;
            });
        } catch (\Exception $e) {
            Log::error('GeolocalizacaoService: Erro ao obter coordenadas', [
                'endereco' => $endereco,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Calcular distância entre dois pontos usando a fórmula de Haversine
     */
    public function calcularDistanciaHaversine(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Raio da Terra em quilômetros

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + 
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * 
             sin($dLon/2) * sin($dLon/2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return round($earthRadius * $c, 2);
    }

    /**
     * Calcular distância do endereço base usando API DistanceMatrix
     */
    public function calcularDistanciaDoEnderecoBase(string $enderecoDestino): ?array
    {
        try {
            $cacheKey = 'distance_' . md5(self::ENDERECO_BASE . '_' . $enderecoDestino);
            
            return Cache::remember($cacheKey, 86400, function () use ($enderecoDestino) {
                $response = Http::timeout(30)->get($this->distanceMatrixBaseUrl . '/maps/api/distancematrix/json', [
                    'origins' => self::ENDERECO_BASE,
                    'destinations' => $enderecoDestino,
                    'key' => $this->distanceMatrixApiKey,
                    'units' => 'metric'
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    
                    if ($data['status'] === 'OK' && 
                        !empty($data['rows']) && 
                        !empty($data['rows'][0]['elements']) &&
                        $data['rows'][0]['elements'][0]['status'] === 'OK') {
                        
                        $element = $data['rows'][0]['elements'][0];
                        
                        return [
                            'distancia_km' => round($element['distance']['value'] / 1000, 2),
                            'duracao_segundos' => $element['duration']['value'],
                            'distancia_texto' => $element['distance']['text'],
                            'duracao_texto' => $element['duration']['text']
                        ];
                    }
                }

                Log::warning('GeolocalizacaoService: Não foi possível calcular distância via API', [
                    'origem' => self::ENDERECO_BASE,
                    'destino' => $enderecoDestino,
                    'response' => $response->json()
                ]);

                return null;
            });
        } catch (\Exception $e) {
            Log::error('GeolocalizacaoService: Erro ao calcular distância via API', [
                'origem' => self::ENDERECO_BASE,
                'destino' => $enderecoDestino,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Processar geolocalização de um endereço local
     */
    public function processarGeolocalizacao(EnderecoLocal $endereco): bool
    {
        try {
            $enderecoCompleto = $endereco->endereco_completo;
            
            // Primeiro tenta obter coordenadas
            $coordenadas = $this->obterCoordenadas($enderecoCompleto);
            
            if ($coordenadas) {
                $endereco->latitude = $coordenadas['latitude'];
                $endereco->longitude = $coordenadas['longitude'];
                
                // Calcula distância usando Haversine com as coordenadas obtidas
                $distanciaHaversine = $this->calcularDistanciaHaversine(
                    self::COORDENADAS_BASE_LAT,
                    self::COORDENADAS_BASE_LON,
                    $coordenadas['latitude'],
                    $coordenadas['longitude']
                );
                
                $endereco->distancia_km = $distanciaHaversine;
                $endereco->save();
                
                Log::info('GeolocalizacaoService: Geolocalização processada com sucesso', [
                    'endereco_id' => $endereco->id,
                    'latitude' => $coordenadas['latitude'],
                    'longitude' => $coordenadas['longitude'],
                    'distancia_km' => $distanciaHaversine
                ]);
                
                return true;
            }
            
            // Se não conseguiu coordenadas, tenta calcular distância diretamente via API
            $distanciaApi = $this->calcularDistanciaDoEnderecoBase($enderecoCompleto);
            
            if ($distanciaApi) {
                $endereco->distancia_km = $distanciaApi['distancia_km'];
                $endereco->save();
                
                Log::info('GeolocalizacaoService: Distância calculada via API', [
                    'endereco_id' => $endereco->id,
                    'distancia_km' => $distanciaApi['distancia_km']
                ]);
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('GeolocalizacaoService: Erro ao processar geolocalização', [
                'endereco_id' => $endereco->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }

    /**
     * Processar geolocalização em lote para endereços sem coordenadas
     */
    public function processarLoteGeolocalizacao(int $limite = 50): array
    {
        $enderecosSemGeo = EnderecoLocal::semGeolocalizacao()
                                     ->ativos()
                                     ->limit($limite)
                                     ->get();

        $resultados = [
            'processados' => 0,
            'sucessos' => 0,
            'erros' => 0,
            'detalhes' => []
        ];

        foreach ($enderecosSemGeo as $endereco) {
            $resultados['processados']++;
            
            if ($this->processarGeolocalizacao($endereco)) {
                $resultados['sucessos']++;
                $resultados['detalhes'][] = [
                    'id' => $endereco->id,
                    'status' => 'sucesso',
                    'distancia_km' => $endereco->fresh()->distancia_km
                ];
            } else {
                $resultados['erros']++;
                $resultados['detalhes'][] = [
                    'id' => $endereco->id,
                    'status' => 'erro'
                ];
            }
            
            // Delay para não sobrecarregar a API
            usleep(500000); // 0.5 segundos
        }

        Log::info('GeolocalizacaoService: Processamento em lote concluído', $resultados);

        return $resultados;
    }
}
