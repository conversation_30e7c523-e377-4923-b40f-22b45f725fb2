<?php

require_once 'vendor/autoload.php';

use Domain\IceExchange\Models\VolumeEvent;
use Domain\NOTFIS\Models\NotfisVolume;

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== MONITORAMENTO DE EVENTOS ===\n\n";

// 1. Verificar estado inicial da tabela
echo "1. Estado inicial da tabela:\n";
$initialCount = VolumeEvent::count();
echo "   Total de registros: $initialCount\n\n";

if ($initialCount > 0) {
    echo "   Últimos registros:\n";
    $latestEvents = VolumeEvent::orderBy('id', 'desc')->take(5)->get();
    foreach ($latestEvents as $event) {
        echo "   - ID: {$event->id} | Tipo: {$event->event_type} | Volume: {$event->getVolumeCode()} | Criado: {$event->created_at}\n";
    }
    echo "\n";
}

// 2. Criar um evento de teste programaticamente
echo "2. Criando evento de teste programaticamente...\n";

// Buscar um volume existente ou usar um código de teste
$testVolumeCode = 'P02279324000136000367857003354870807480001';
$volume = NotfisVolume::where('volume_code', $testVolumeCode)->first();

if ($volume) {
    echo "   Volume encontrado: {$volume->volume_code} (ID: {$volume->id})\n";
    echo "   Customer ID: {$volume->notfis->sender_customer_id}\n";
    
    try {
        // Criar evento de montagem de caixa
        $event = VolumeEvent::create([
            'notfis_volume_id' => $volume->id,
            'event_type' => 'filling',
            'event_date' => now(),
            'due_date' => now()->addHours(24),
            'user_id' => 162, // ID do usuário teste
            'ice_type_id' => null,
            'warehouse_id' => null,
            'deadline_hours' => 24,
            'emit_label' => false,
        ]);
        
        echo "   ✅ Evento criado com sucesso!\n";
        echo "   - ID: {$event->id}\n";
        echo "   - Tipo: {$event->event_type}\n";
        echo "   - Data: {$event->event_date}\n";
        echo "   - Due Date: {$event->due_date}\n";
        echo "   - Deadline Hours: {$event->deadline_hours}\n";
        echo "   - Volume ID: {$event->notfis_volume_id}\n";
        
    } catch (Exception $e) {
        echo "   ❌ Erro ao criar evento: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "   ❌ Volume não encontrado: $testVolumeCode\n";
}

echo "\n";

// 3. Verificar estado final da tabela
echo "3. Estado final da tabela:\n";
$finalCount = VolumeEvent::count();
echo "   Total de registros: $finalCount\n";
echo "   Diferença: " . ($finalCount - $initialCount) . " novo(s) registro(s)\n\n";

if ($finalCount > $initialCount) {
    echo "   Últimos registros:\n";
    $latestEvents = VolumeEvent::orderBy('id', 'desc')->take(3)->get();
    foreach ($latestEvents as $event) {
        echo "   - ID: {$event->id} | Tipo: {$event->event_type} | Volume: {$event->getVolumeCode()} | Criado: {$event->created_at}\n";
    }
}

// 4. Testar validação de troca
echo "\n4. Testando validação de troca após montagem de caixa:\n";
$fillingEvents = VolumeEvent::where('notfis_volume_id', $volume->id ?? 0)
    ->where('event_type', 'filling')
    ->count();

echo "   Eventos de montagem de caixa para o volume: $fillingEvents\n";

if ($fillingEvents > 0) {
    echo "   ✅ Agora seria possível fazer manutenção de gelo para este volume\n";
} else {
    echo "   ❌ Ainda não seria possível fazer troca (sem montagem de caixa)\n";
}

echo "\n=== FIM DO MONITORAMENTO ===\n";

