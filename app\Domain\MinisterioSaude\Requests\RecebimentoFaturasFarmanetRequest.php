<?php

namespace Domain\MinisterioSaude\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RecebimentoFaturasFarmanetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id_gestor' => 'required|string|max:22',
            'codigo_programa' => 'sometimes|integer|in:5,24,25,28',
            'ano_referencia' => 'required|integer|digits:4|min:2020|max:2030',
            'mes_referencia' => 'nullable|integer|min:1|max:12',
            'numero_fatura' => 'nullable|string|max:50',
            'status_fatura' => 'nullable|integer|in:1,2,3,4',
            'data_inicio' => 'nullable|date_format:Y-m-d',
            'data_fim' => 'nullable|date_format:Y-m-d|after_or_equal:data_inicio',
            'access_token' => 'required|string|max:40',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'id_gestor.required' => 'O ID do gestor é obrigatório',
            'id_gestor.string' => 'O ID do gestor deve ser uma string',
            'id_gestor.max' => 'O ID do gestor não pode ter mais de 22 caracteres',
            
            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
            'codigo_programa.in' => 'O código do programa deve ser 5 (Diabetes), 24 (Dose Certa), 25 (Saúde da Mulher) ou 28 (Arboviroses)',
            
            'ano_referencia.required' => 'O ano de referência é obrigatório',
            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
            'ano_referencia.digits' => 'O ano de referência deve ter 4 dígitos',
            'ano_referencia.min' => 'O ano de referência deve ser maior que 2019',
            'ano_referencia.max' => 'O ano de referência deve ser menor que 2031',
            
            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
            'mes_referencia.min' => 'O mês de referência deve ser entre 1 e 12',
            'mes_referencia.max' => 'O mês de referência deve ser entre 1 e 12',
            
            'numero_fatura.string' => 'O número da fatura deve ser uma string',
            'numero_fatura.max' => 'O número da fatura não pode ter mais de 50 caracteres',
            
            'status_fatura.integer' => 'O status da fatura deve ser um número inteiro',
            'status_fatura.in' => 'O status deve ser: 1 (Pendente), 2 (Processada), 3 (Paga), 4 (Cancelada)',
            
            'data_inicio.date_format' => 'A data de início deve estar no formato Y-m-d',
            'data_fim.date_format' => 'A data de fim deve estar no formato Y-m-d',
            'data_fim.after_or_equal' => 'A data de fim deve ser posterior ou igual à data de início',
            
            'access_token.required' => 'O token de acesso é obrigatório',
            'access_token.string' => 'O token de acesso deve ser uma string',
            'access_token.max' => 'O token de acesso não pode ter mais de 40 caracteres',
        ];
    }
}
