<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\PedidoFarmanet;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AtualizarPedidoFarmanetService
{
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
            'verify' => false,
        ]);
        
        $environment = config('ministerio_saude.farmanet.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
    }

    /**
     * Atualiza status de um pedido Farmanet no Ministério da Saúde
     *
     * @param array $params
     * @return array
     */
    public function atualizarPedido(array $params): array
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.atualizar_pedido');
            $url = $this->baseUrl . $endpoint . '/' . $params['id_pedido_ms'];
            
            Log::info('AtualizarPedidoFarmanetService - Iniciando atualização', [
                'url' => $url,
                'params' => $params
            ]);

            $payload = [
                'id_gestor' => $params['id_gestor'],
                'status_pedido' => $params['status_pedido'],
                'observacoes' => $params['observacoes'] ?? null,
                'data_atualizacao' => $params['data_atualizacao']
            ];

            $response = $this->client->put($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $params['access_token'],
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => $payload
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            
            Log::info('AtualizarPedidoFarmanetService - Atualização realizada', [
                'status_code' => $response->getStatusCode(),
                'id_pedido_ms' => $params['id_pedido_ms'],
                'novo_status' => $params['status_pedido']
            ]);

            // Atualizar o pedido localmente
            $this->atualizarPedidoLocal($params);

            return [
                'success' => true,
                'data' => $data,
                'message' => 'Pedido atualizado com sucesso',
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            Log::error('AtualizarPedidoFarmanetService - Erro na requisição', [
                'message' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'params' => $params
            ]);

            return [
                'success' => false,
                'error' => 'Erro ao atualizar pedido Farmanet: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('AtualizarPedidoFarmanetService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $params
            ]);

            return [
                'success' => false,
                'error' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Atualiza o pedido na base local
     *
     * @param array $params
     * @return void
     */
    private function atualizarPedidoLocal(array $params): void
    {
        try {
            $pedido = PedidoFarmanet::where('id_pedido_ms', $params['id_pedido_ms'])
                                   ->where('id_gestor', $params['id_gestor'])
                                   ->first();

            if ($pedido) {
                $pedido->update([
                    'status_pedido' => $params['status_pedido'],
                    'observacoes' => $params['observacoes'] ?? $pedido->observacoes,
                    'data_ultima_atualizacao' => Carbon::parse($params['data_atualizacao'])
                ]);

                Log::info('Pedido Farmanet atualizado localmente', [
                    'pedido_id' => $pedido->id,
                    'id_pedido_ms' => $pedido->id_pedido_ms,
                    'novo_status' => $params['status_pedido']
                ]);
            } else {
                Log::warning('Pedido Farmanet não encontrado para atualização local', [
                    'id_pedido_ms' => $params['id_pedido_ms'],
                    'id_gestor' => $params['id_gestor']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Erro ao atualizar pedido Farmanet localmente', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Valida se um pedido pode ser atualizado
     *
     * @param string $idPedidoMs
     * @param string $idGestor
     * @param int $novoStatus
     * @return array
     */
    public function validarAtualizacaoPedido(string $idPedidoMs, string $idGestor, int $novoStatus): array
    {
        try {
            $pedido = PedidoFarmanet::where('id_pedido_ms', $idPedidoMs)
                                   ->where('id_gestor', $idGestor)
                                   ->first();

            if (!$pedido) {
                return [
                    'valid' => false,
                    'message' => 'Pedido não encontrado'
                ];
            }

            // Regras de negócio para validação de status
            $statusValidos = [
                1 => [2, 3, 4, 5], // Pendente pode ir para qualquer status
                2 => [3, 4, 5],    // Em Processamento pode ir para Aprovado, Rejeitado ou Cancelado
                3 => [5],          // Aprovado só pode ser Cancelado
                4 => [],           // Rejeitado não pode mudar
                5 => []            // Cancelado não pode mudar
            ];

            $statusAtual = $pedido->status_pedido;

            if (!isset($statusValidos[$statusAtual])) {
                return [
                    'valid' => false,
                    'message' => 'Status atual do pedido é inválido'
                ];
            }

            if (!in_array($novoStatus, $statusValidos[$statusAtual])) {
                return [
                    'valid' => false,
                    'message' => 'Transição de status não permitida',
                    'status_atual' => $statusAtual,
                    'novo_status' => $novoStatus
                ];
            }

            return [
                'valid' => true,
                'pedido' => $pedido,
                'message' => 'Atualização permitida'
            ];

        } catch (\Exception $e) {
            Log::error('Erro ao validar atualização do pedido', [
                'id_pedido_ms' => $idPedidoMs,
                'id_gestor' => $idGestor,
                'novo_status' => $novoStatus,
                'error' => $e->getMessage()
            ]);

            return [
                'valid' => false,
                'message' => 'Erro interno na validação: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Lista os status possíveis para um pedido
     *
     * @return array
     */
    public function obterStatusPossiveis(): array
    {
        return [
            1 => 'Pendente',
            2 => 'Em Processamento',
            3 => 'Aprovado',
            4 => 'Rejeitado',
            5 => 'Cancelado'
        ];
    }
}
