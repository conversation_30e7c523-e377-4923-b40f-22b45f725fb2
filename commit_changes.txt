commit 14285edac1f25fa5dc865e475bec489f14da9843
Author: <PERSON> <<EMAIL>>
Date:   Wed Sep 17 04:07:16 2025 -0300

    Reestrutura de pasta, padronização dos retornos, exceções, logs (espaço para melhora) e implementação das apis

diff --git a/app/Domain/MinisterioSaude/Controllers/ConsultarItensController.php b/app/Domain/MinisterioSaude/Controllers/ConsultarItensController.php
deleted file mode 100644
index 9bf04fd..0000000
--- a/app/Domain/MinisterioSaude/Controllers/ConsultarItensController.php
+++ /dev/null
@@ -1,76 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Controllers;
-
-use Domain\MinisterioSaude\Services\ConsultarItensService;
-use Illuminate\Http\JsonResponse;
-use Illuminate\Http\Request;
-use App\Controllers\Controller;
-use Illuminate\Support\Facades\Log;
-use Illuminate\Support\Facades\Validator;
-
-class ConsultarItensController extends Controller
-{
-    private ConsultarItensService $consultarItensService;
-
-    public function __construct(ConsultarItensService $consultarItensService)
-    {
-        $this->consultarItensService = $consultarItensService;
-    }
-
-    /**
-     * API 1.4 - Consultar Itens do Sistema GSNET
-     * 
-     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
-     *
-     * @param Request $request
-     * @return JsonResponse
-     */
-    public function consultarItens(Request $request): JsonResponse
-    {
-        try {
-            // Validação dos dados de entrada
-            $validator = Validator::make($request->all(), [
-                'Data' => 'required|array',
-                'Data.ListaItens' => 'required|array|min:1|max:100',
-                'Data.ListaItens.*.CodigoMaterial' => 'required|numeric|digits_between:1,11',
-                'AccessToken' => 'required|string|max:240'
-            ]);
-
-            if ($validator->fails()) {
-                return response()->json([
-                    'success' => false,
-                    'message' => 'Dados inválidos fornecidos.',
-                    'errors' => $validator->errors()
-                ], 422);
-            }
-
-            $validatedData = $validator->validated();
-
-            // Chamar o serviço para consultar os itens
-            $result = $this->consultarItensService->consultarItens($validatedData);
-
-            if ($result['success']) {
-                return response()->json([
-                    'success' => true,
-                    'data' => $result['data'],
-                    'message' => $result['message'],
-                    'timestamp' => $result['timestamp']
-                ], 200);
-            } else {
-                return response()->json([
-                    'success' => false,
-                    'message' => $result['message'],
-                    'timestamp' => $result['timestamp']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            return response()->json([
-                'success' => false,
-                'message' => 'Erro interno: ' . $e->getMessage(),
-                'timestamp' => now()->toISOString()
-            ], 500);
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Controllers/FaturaFarmanetController.php b/app/Domain/MinisterioSaude/Controllers/FaturaFarmanetController.php
new file mode 100644
index 0000000..0c4f283
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Controllers/FaturaFarmanetController.php
@@ -0,0 +1,149 @@
+<?php
+
+namespace Domain\MinisterioSaude\Controllers;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Illuminate\Http\JsonResponse;
+use App\Controllers\Controller;
+// Requests
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\InserirStatusFaturaRequest;
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarStatusOpRequest;
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarEnderecoRequest;
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\ConsultarFaturasRequest;
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\AtualizarStatusFaturaRequest;
+use Domain\MinisterioSaude\Requests\FaturaFarmamet\ReceberFaturasRequest;
+// Inputs
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\ConsultarStatusInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\CriarStatusInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input\ConsultaEnderecoInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\AtualizarStatusFaturaInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ReceberFaturasInput;
+// Services
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\StatusOperadorService;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\FaturaGsnetService;
+
+class FaturaFarmanetController extends Controller
+{
+    use HasLog;
+
+    private StatusOperadorService $statusService;
+    private EnderecoLocalService $enderecoService;
+    private FaturaGsnetService $faturaService;
+
+    public function __construct(
+        StatusOperadorService $statusService,
+        EnderecoLocalService $enderecoService,
+        FaturaGsnetService $faturaService
+    ) {
+        $this->statusService = $statusService;
+        $this->enderecoService = $enderecoService;
+        $this->faturaService = $faturaService;
+    }
+
+    /**
+     * API 1.1 - Inserir Status na Fatura
+     *
+     * @param InserirStatusFaturaRequest $request
+     * @return JsonResponse
+     */
+    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->statusService->criarStatus(CriarStatusInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@inserirStatusFatura - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 1.2 - Consultar Status do Operador
+     *
+     * @param ConsultarStatusOpRequest $request
+     * @return JsonResponse
+     */
+    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->statusService->consultarStatus(ConsultarStatusInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@consultarStatusOp - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 1.3 - Consultar Endereço Local
+     *
+     * @param ConsultarEnderecoRequest $request
+     * @return JsonResponse
+     */
+    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->enderecoService->consultarEndereco(ConsultaEnderecoInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@consultarEndereco - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 2.1 - Consultar Faturas (Recebe)
+     *
+     * @param ConsultarFaturasRequest $request
+     * @return JsonResponse
+     */
+    public function consultarFaturas(ConsultarFaturasRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->faturaService->consultarFaturas(ConsultaFaturaInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@consultarFaturas - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 2.2 - Atualizar Status da Fatura (Envio)
+     *
+     * @param AtualizarStatusFaturaRequest $request
+     * @return JsonResponse
+     */
+    public function atualizarStatusFatura(AtualizarStatusFaturaRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->faturaService->atualizarStatusFatura(AtualizarStatusFaturaInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@atualizarStatusFatura - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 3.3 - Recebimento Faturas Farmanet
+     *
+     * Serviço utilizado para gerar Fatura do pedido Farmanet no GSNET
+     *
+     * @param ReceberFaturasRequest $request
+     * @return JsonResponse
+     */
+    public function receberFaturas(ReceberFaturasRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->faturaService->receberFaturas(ReceberFaturasInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('FaturaFarmanetController@receberFaturas - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Controllers/ItemController.php b/app/Domain/MinisterioSaude/Controllers/ItemController.php
new file mode 100644
index 0000000..2d1e92f
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Controllers/ItemController.php
@@ -0,0 +1,64 @@
+<?php
+
+namespace Domain\MinisterioSaude\Controllers;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Illuminate\Http\JsonResponse;
+use App\Controllers\Controller;
+// Requests
+use Domain\MinisterioSaude\Requests\Item\ConsultarItensRequest;
+use Domain\MinisterioSaude\Requests\Item\ObterPrecoMedioItensRequest;
+// Inputs
+use Domain\MinisterioSaude\Services\Item\Input\ObterPrecoMedioItensInput;
+use Domain\MinisterioSaude\Services\Item\Input\ConsultarItensInput;
+// Services
+use Domain\MinisterioSaude\Services\Item\ItemService;
+
+class ItemController extends Controller
+{
+    use HasLog;
+
+    private ItemService $itemService;
+
+    public function __construct(ItemService $itemService)
+    {
+        $this->itemService = $itemService;
+    }
+
+    /**
+     * API 1.4 - Consultar Itens do Sistema GSNET
+     *
+     * Serviço utilizado pelo Operador Logístico para consultar os Itens "código GSNET"
+     *
+     * @param ConsultarItensRequest $request
+     * @return JsonResponse
+     */
+    public function consultarItens(ConsultarItensRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->itemService->consultarItens(ConsultarItensInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('ItemController@consultarItens - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 3.4 - Obter Preço Médio de Itens
+     *
+     * @param ObterPrecoMedioItensRequest $request
+     * @return JsonResponse
+     */
+    public function obterPrecoMedio(ObterPrecoMedioItensRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->itemService->obterPrecoMedio(ObterPrecoMedioItensInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('ItemController@obterPrecoMedio - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController.php b/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController.php
deleted file mode 100644
index d02749d..0000000
--- a/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController.php
+++ /dev/null
@@ -1,210 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Controllers;
-
-use App\Controllers\Controller;
-use Domain\MinisterioSaude\Requests\InserirStatusFaturaRequest;
-use Domain\MinisterioSaude\Requests\ConsultarStatusOpRequest;
-use Domain\MinisterioSaude\Requests\ConsultarEnderecoRequest;
-use Domain\MinisterioSaude\Requests\ConsultarFaturasRequest;
-use Domain\MinisterioSaude\Requests\AtualizarStatusFaturaRequest;
-use Domain\MinisterioSaude\Services\StatusOperadorService;
-use Domain\MinisterioSaude\Services\EnderecoLocalService;
-use Domain\MinisterioSaude\Services\FaturaGsnetService;
-use Illuminate\Http\JsonResponse;
-use Illuminate\Support\Facades\Log;
-
-class MinisterioSaudeController extends Controller
-{
-    private $statusService;
-    private $enderecoService;
-
-    public function __construct(
-        StatusOperadorService $statusService,
-        EnderecoLocalService $enderecoService
-    ) {
-        $this->statusService = $statusService;
-        $this->enderecoService = $enderecoService;
-    }
-
-    /**
-     * API 1.1 - Inserir Status na Fatura
-     * 
-     * @param InserirStatusFaturaRequest $request
-     * @return JsonResponse
-     */
-    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
-    {
-        try {
-            $data = $request->validated();
-            
-            $result = $this->statusService->criarStatus(
-                $data['Data']['IdOrigem'],
-                $data['Data']['NomeStatus'],
-                $data['Data']['DescricaoStatus']
-            );
-
-            if ($result['success']) {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@inserirStatusFatura - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 1.2 - Consultar Status do Operador
-     * 
-     * @param ConsultarStatusOpRequest $request
-     * @return JsonResponse
-     */
-    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
-    {
-        try {
-            $validated = $request->validated();
-            
-            $result = $this->statusService->consultarStatus(
-                $validated['IdStatus'] ?? null,
-                $validated['IdOrigem'] ?? null,
-                $validated['NomeStatus'] ?? null
-            );
-
-            if ($result['success']) {
-                return response()->json($result['data'], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@consultarStatusOp - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 1.3 - Consultar Endereço Local
-     * 
-     * @param ConsultarEnderecoRequest $request
-     * @return JsonResponse
-     */
-    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
-    {
-        try {
-            $validated = $request->validated();
-            
-            $result = $this->enderecoService->consultarEndereco(
-                $validated['IdGestor'],
-                $validated['IdLocal']
-            );
-
-            if ($result['success']) {
-                return response()->json([
-                    'Message' => $result['message'],
-                    'Data' => $result['data']
-                ], 200);
-            } else {
-                // Para ResultCode 204, retornar 200 com a mensagem de dados não encontrados
-                return response()->json([
-                    'Message' => $result['message'],
-                    'Data' => $result['data']
-                ], 200);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@consultarEndereco - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 2.1 - Consultar Faturas (Recebe)
-     * 
-     * @param ConsultarFaturasRequest $request
-     * @return JsonResponse
-     */
-    public function consultarFaturas(ConsultarFaturasRequest $request): JsonResponse
-    {
-        try {
-            $data = $request->validated();
-            
-            $faturaService = app(\Domain\MinisterioSaude\Services\FaturaGsnetService::class);
-            $result = $faturaService->consultarFaturas($data);
-
-            return response()->json($result, 200);
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@consultarFaturas - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 2.2 - Atualizar Status da Fatura (Envio)
-     * 
-     * @param AtualizarStatusFaturaRequest $request
-     * @return JsonResponse
-     */
-    public function atualizarStatusFatura(AtualizarStatusFaturaRequest $request): JsonResponse
-    {
-        try {
-            $data = $request->validated();
-            
-            $faturaService = app(\Domain\MinisterioSaude\Services\FaturaGsnetService::class);
-            $result = $faturaService->atualizarStatusFatura($data);
-
-            if ($result['success']) {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@atualizarStatusFatura - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController_MVP.php b/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController_MVP.php
deleted file mode 100644
index a60b5a5..0000000
--- a/app/Domain/MinisterioSaude/Controllers/MinisterioSaudeController_MVP.php
+++ /dev/null
@@ -1,142 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Controllers;
-
-use App\Controllers\Controller;
-use Domain\MinisterioSaude\Requests\InserirStatusFaturaRequest;
-use Domain\MinisterioSaude\Requests\ConsultarStatusOpRequest;
-use Domain\MinisterioSaude\Requests\ConsultarEnderecoRequest;
-use Domain\MinisterioSaude\Services\StatusOperadorService;
-use Domain\MinisterioSaude\Services\EnderecoLocalService;
-use Illuminate\Http\JsonResponse;
-use Illuminate\Support\Facades\Log;
-
-class MinisterioSaudeController extends Controller
-{
-    private $statusService;
-    private $enderecoService;
-
-    public function __construct(
-        StatusOperadorService $statusService,
-        EnderecoLocalService $enderecoService
-    ) {
-        $this->statusService = $statusService;
-        $this->enderecoService = $enderecoService;
-    }
-
-    /**
-     * API 1.1 - Inserir Status na Fatura
-     * 
-     * @param InserirStatusFaturaRequest $request
-     * @return JsonResponse
-     */
-    public function inserirStatusFatura(InserirStatusFaturaRequest $request): JsonResponse
-    {
-        try {
-            $data = $request->validated();
-            
-            $result = $this->statusService->criarStatus(
-                $data['Data']['IdOrigem'],
-                $data['Data']['NomeStatus'],
-                $data['Data']['DescricaoStatus']
-            );
-
-            if ($result['success']) {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@inserirStatusFatura - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 1.2 - Consultar Status do Operador
-     * 
-     * @param ConsultarStatusOpRequest $request
-     * @return JsonResponse
-     */
-    public function consultarStatusOp(ConsultarStatusOpRequest $request): JsonResponse
-    {
-        try {
-            $validated = $request->validated();
-            
-            $result = $this->statusService->consultarStatus(
-                $validated['IdStatus'] ?? null,
-                $validated['IdOrigem'] ?? null,
-                $validated['NomeStatus'] ?? null
-            );
-
-            if ($result['success']) {
-                return response()->json($result['data'], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message']
-                ], 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@consultarStatusOp - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-
-    /**
-     * API 1.3 - Consultar Endereço Local
-     * 
-     * @param ConsultarEnderecoRequest $request
-     * @return JsonResponse
-     */
-    public function consultarEndereco(ConsultarEnderecoRequest $request): JsonResponse
-    {
-        try {
-            $validated = $request->validated();
-            
-            $result = $this->enderecoService->consultarEndereco(
-                $validated['IdGestor'],
-                $validated['IdLocal']
-            );
-
-            if ($result['success']) {
-                return response()->json([
-                    'Message' => $result['message'],
-                    'Data' => $result['data']
-                ], 200);
-            } else {
-                return response()->json([
-                    'Message' => $result['message'],
-                    'Data' => $result['data']
-                ], $result['data']['ResultCode'] === 204 ? 204 : 400);
-            }
-
-        } catch (\Exception $e) {
-            Log::error('MinisterioSaudeController@consultarEndereco - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return response()->json([
-                'Message' => 'Erro interno do servidor'
-            ], 500);
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Controllers/PlanejamentoController.php b/app/Domain/MinisterioSaude/Controllers/PlanejamentoController.php
new file mode 100644
index 0000000..db79e0d
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Controllers/PlanejamentoController.php
@@ -0,0 +1,67 @@
+<?php
+
+namespace Domain\MinisterioSaude\Controllers;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Illuminate\Http\JsonResponse;
+use App\Controllers\Controller;
+use Illuminate\Support\Facades\Log;
+// Requests
+use Domain\MinisterioSaude\Requests\Planejamento\ConsultarPedidosRequest;
+use Domain\MinisterioSaude\Requests\Planejamento\AtualizarPedidoRequest;
+// Inputs
+use Domain\MinisterioSaude\Services\Planejamento\Input\AtualizarPedidoInput;
+use Domain\MinisterioSaude\Services\Planejamento\Input\ConsultarPedidosInput;
+// Services
+use Domain\MinisterioSaude\Services\Planejamento\PlanejamentoService;
+
+class PlanejamentoController extends Controller
+{
+    use HasLog;
+
+    private PlanejamentoService $planejamentoService;
+
+    public function __construct(PlanejamentoService $planejamentoService)
+    {
+        $this->planejamentoService = $planejamentoService;
+    }
+
+    /**
+     * API 3.1 Consultar Pedidos Farmanet
+     *
+     * Serviço utilizado para consultar as faturas do GSNET.
+     *
+     * @param ConsultarPedidosRequest $request
+     * @return JsonResponse
+     */
+    public function consultarPedidos(ConsultarPedidosRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->planejamentoService->consultarPedidos(ConsultarPedidosInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('ItemController@consultarItens - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+
+    /**
+     * API 3.2 Atualizar Pedidos Farmanet
+     *
+     * Serviço utilizado para atualizar os pedidos do GSNET.
+     *
+     * @param AtualizarPedidoRequest $request
+     * @return JsonResponse
+     */
+    public function atualizarPedido(AtualizarPedidoRequest $request): JsonResponse
+    {
+        try {
+            $result = $this->planejamentoService->atualizarPedido(AtualizarPedidoInput::fromArray($request->validated()));
+            return $result->toResponse();
+        } catch (\Exception $e) {
+            $this->logError('ItemController@consultarItens - Erro', $e);
+            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
+        }
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Exceptions/CustomMessageException.php b/app/Domain/MinisterioSaude/Exceptions/CustomMessageException.php
new file mode 100644
index 0000000..fcd1e51
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Exceptions/CustomMessageException.php
@@ -0,0 +1,41 @@
+<?php
+
+namespace Domain\MinisterioSaude\Exceptions;
+
+use Exception;
+use Throwable;
+
+class CustomMessageException extends Exception
+{
+    private $data;
+    private string $errorCode;
+
+    public function __construct(string $message, $data, string $errorCode = "INVALID_OPERATION", int $code = 400, ?Throwable $previous = null)
+    {
+        parent::__construct($message, $code, $previous);
+        $this->data = $data;
+        $this->errorCode = $errorCode;
+    }
+
+    public function setData($data)
+    {
+        $this->data = $data;
+        return $this;
+    }
+
+    public function getData()
+    {
+        return $this->data;
+    }
+
+    public function setErrorCode(string $code)
+    {
+        $this->errorCode = $code;
+        return $this;
+    }
+
+    public function getErrorCode()
+    {
+        return $this->errorCode;
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Exceptions/DuplicateRecordException.php b/app/Domain/MinisterioSaude/Exceptions/DuplicateRecordException.php
new file mode 100644
index 0000000..75ee91a
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Exceptions/DuplicateRecordException.php
@@ -0,0 +1,27 @@
+<?php
+
+namespace Domain\MinisterioSaude\Exceptions;
+
+use Exception;
+use Throwable;
+
+class DuplicateRecordException extends Exception
+{
+    private array $duplicatedFields;
+
+    public function __construct(string $message, array $duplicatedFields = [], int $code = 422, ?Throwable $previous = null)
+    {
+        parent::__construct($message, $code, $previous);
+        $this->duplicatedFields = $duplicatedFields;
+    }
+
+    public function getDuplicatedFields(): array
+    {
+        return $this->duplicatedFields;
+    }
+
+    public function hasDuplicatedFields(): bool
+    {
+        return !empty($this->duplicatedFields);
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Helpers/ServiceResponse.php b/app/Domain/MinisterioSaude/Helpers/ServiceResponse.php
new file mode 100644
index 0000000..2854428
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Helpers/ServiceResponse.php
@@ -0,0 +1,367 @@
+<?php
+
+namespace Domain\MinisterioSaude\Helpers;
+
+use Throwable;
+
+class ServiceResponse
+{
+    public $data = null;
+    public bool $success = true;
+    public string $message = '';
+    public ?string $errorCode = null;
+    public array $errors = [];
+
+    public function __construct($data = null)
+    {
+        if ($data !== null) {
+            $this->data = $data;
+        }
+    }
+
+    public function setSuccess(bool $success = true): self
+    {
+        $this->success = $success;
+        return $this;
+    }
+
+    public function setFailure(string $message, $data = null): self
+    {
+        $this->success = false;
+        $this->message = $message;
+
+        if ($data !== null) {
+            $this->data = $data;
+        }
+
+        return $this;
+    }
+
+    public function setFailureFromException(Throwable $exception): self
+    {
+        $this->success = false;
+        $this->message = $exception->getMessage();
+        return $this;
+    }
+
+    public function setData($data): self
+    {
+        $this->data = $data;
+        return $this;
+    }
+
+    public function setNullData(): self
+    {
+        $this->data = null;
+        return $this;
+    }
+
+    public function setMessage(string $message): self
+    {
+        $this->message = $message;
+        return $this;
+    }
+
+    public function setErrorCode(string $code): self
+    {
+        $this->errorCode = $code;
+        return $this;
+    }
+
+    public function addError(string $field, string $message): self
+    {
+        $this->errors[$field][] = $message;
+        return $this;
+    }
+
+    public function setErrors(array $errors): self
+    {
+        $this->errors = $errors;
+        return $this;
+    }
+
+    public function clearErrors(): self
+    {
+        $this->errors = [];
+        return $this;
+    }
+
+    public function addErrors(array $errors): self
+    {
+        foreach ($errors as $field => $messages) {
+            if (is_array($messages)) {
+                foreach ($messages as $message) {
+                    $this->addError($field, $message);
+                }
+            } else {
+                $this->addError($field, $messages);
+            }
+        }
+        return $this;
+    }
+
+    public static function success($data = null, string $message = ''): self
+    {
+        $response = new self($data);
+        $response->message = $message;
+        return $response;
+    }
+
+    public static function failure(string $message, $data = null): self
+    {
+        return (new self())->setFailure($message, $data);
+    }
+
+    public static function fromException(Throwable $exception, $data = null): self
+    {
+        return (new self($data))->setFailureFromException($exception);
+    }
+
+    public static function validationError(array $errors, string $message = 'Dados inválidos'): self
+    {
+        return (new self())
+            ->setFailure($message)
+            ->setErrors($errors)
+            ->setErrorCode('VALIDATION_ERROR');
+    }
+
+    public static function notFound(string $message = 'Recurso não encontrado'): self
+    {
+        return (new self())
+            ->setFailure($message)
+            ->setErrorCode('NOT_FOUND');
+    }
+
+    public static function unauthorized(string $message = 'Não autorizado'): self
+    {
+        return (new self())
+            ->setFailure($message)
+            ->setErrorCode('UNAUTHORIZED');
+    }
+
+    public static function forbidden(string $message = 'Acesso negado'): self
+    {
+        return (new self())
+            ->setFailure($message)
+            ->setErrorCode('FORBIDDEN');
+    }
+
+    public static function internalError(string $message = 'Erro interno do servidor'): self
+    {
+        return (new self())
+            ->setFailure($message)
+            ->setErrorCode('INTERNAL_ERROR');
+    }
+
+    public function isSuccess(): bool
+    {
+        return $this->success;
+    }
+
+    public function isFailure(): bool
+    {
+        return !$this->success;
+    }
+
+    public function hasData(): bool
+    {
+        return $this->data !== null;
+    }
+
+    public function hasMessage(): bool
+    {
+        return !empty($this->message);
+    }
+
+    public function hasErrors(): bool
+    {
+        return !empty($this->errors);
+    }
+
+    public function hasErrorCode(): bool
+    {
+        return $this->errorCode !== null;
+    }
+
+    public function hasError(string $field): bool
+    {
+        return isset($this->errors[$field]);
+    }
+
+    public function getData()
+    {
+        return $this->data;
+    }
+
+    public function getMessage(): string
+    {
+        return $this->message;
+    }
+
+    public function getErrorCode(): ?string
+    {
+        return $this->errorCode;
+    }
+
+    public function getErrors(): array
+    {
+        return $this->errors;
+    }
+
+    public function getErrorsForField(string $field): array
+    {
+        return $this->errors[$field] ?? [];
+    }
+
+    public function getFirstError(string $field = null): ?string
+    {
+        if ($field) {
+            return $this->errors[$field][0] ?? null;
+        }
+
+        foreach ($this->errors as $fieldErrors) {
+            return $fieldErrors[0] ?? null;
+        }
+
+        return null;
+    }
+
+    public function toArray(): array
+    {
+        $result = [
+            'success' => $this->success,
+            'message' => $this->message,
+        ];
+
+        if ($this->data !== null) {
+            $result['data'] = $this->data;
+        }
+
+        if ($this->errorCode) {
+            $result['error_code'] = $this->errorCode;
+        }
+
+        if (!empty($this->errors)) {
+            $result['errors'] = $this->errors;
+        }
+
+        return $result;
+    }
+
+    public function toJson(): string
+    {
+        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
+    }
+
+    public function toResponse(int $statusCode = null): \Illuminate\Http\JsonResponse
+    {
+        $statusCode ??= $this->getDefaultStatusCode();
+        return response()->json($this->toArray(), $statusCode);
+    }
+
+    public function toSuccessResponse(): \Illuminate\Http\JsonResponse
+    {
+        return $this->toResponse(200);
+    }
+
+    public function toErrorResponse(int $statusCode = null): \Illuminate\Http\JsonResponse
+    {
+        $statusCode ??= $this->getDefaultErrorStatusCode();
+        return $this->toResponse($statusCode);
+    }
+
+    private function getDefaultStatusCode(): int
+    {
+        if ($this->success) {
+            return 200;
+        }
+
+        return $this->getDefaultErrorStatusCode();
+    }
+
+    private function getDefaultErrorStatusCode(): int
+    {
+        switch ($this->errorCode) {
+            case 'BAD_REQUEST':
+                $statusCode = 400;
+                break;
+            case 'UNAUTHORIZED':
+                $statusCode = 401;
+                break;
+            case 'FORBIDDEN':
+                $statusCode = 403;
+                break;
+            case 'NOT_FOUND':
+                $statusCode = 404;
+                break;
+            case 'METHOD_NOT_ALLOWED':
+                $statusCode = 405;
+                break;
+            case 'REQUEST_TIMEOUT':
+                $statusCode = 408;
+                break;
+            case 'REQUEST_ENTITY_TOO_LARGE':
+                $statusCode = 413;
+                break;
+            case 'CONFLICT':
+                $statusCode = 409;
+                break;
+            case 'VALIDATION_ERROR':
+                $statusCode = 422;
+                break;
+            case 'TOO_MANY_REQUESTS':
+                $statusCode = 429;
+                break;
+            case 'INTERNAL_ERROR':
+                $statusCode = 500;
+                break;
+            case 'SERVICE_UNAVAILABLE':
+                $statusCode = 503;
+                break;
+            case 'GATEWAY_TIMEOUT':
+                $statusCode = 504;
+                break;
+            default:
+                $statusCode = 400;
+        }
+
+        return $statusCode;
+    }
+
+    public function setValidationErrors(\Illuminate\Support\MessageBag $messageBag): self
+    {
+        $this->errors = $messageBag->toArray();
+        $this->setFailure('Dados inválidos')
+             ->setErrorCode('VALIDATION_ERROR');
+        return $this;
+    }
+
+    public function setValidatorErrors(\Illuminate\Validation\Validator $validator): self
+    {
+        return $this->setValidationErrors($validator->errors());
+    }
+
+    public function when(bool $condition, callable $callback): self
+    {
+        if ($condition) {
+            $callback($this);
+        }
+        return $this;
+    }
+
+    public function unless(bool $condition, callable $callback): self
+    {
+        return $this->when(!$condition, $callback);
+    }
+
+    public function tap(callable $callback): self
+    {
+        $callback($this);
+        return $this;
+    }
+
+    public function __toString(): string
+    {
+        return $this->toJson();
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Providers/MinisterioSaudeServiceProvider.php b/app/Domain/MinisterioSaude/Providers/MinisterioSaudeServiceProvider.php
index a02300b..baf3a9d 100644
--- a/app/Domain/MinisterioSaude/Providers/MinisterioSaudeServiceProvider.php
+++ b/app/Domain/MinisterioSaude/Providers/MinisterioSaudeServiceProvider.php
@@ -3,12 +3,16 @@
 namespace Domain\MinisterioSaude\Providers;
 
 use Illuminate\Support\ServiceProvider;
-use Domain\MinisterioSaude\Services\EnderecoLocalService;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\EnderecoLocalService;
 use Domain\MinisterioSaude\Services\GeolocalizacaoService;
 use Domain\MinisterioSaude\Services\MinisterioSaudeApiService;
-use Domain\MinisterioSaude\Services\StatusOperadorService;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\StatusOperadorService;
 use Domain\MinisterioSaude\Repositories\EnderecoLocalRepository;
 use Domain\MinisterioSaude\Repositories\EnderecoLocalRepositoryInterface;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\StatusOperadorRepository;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\MinisterioSaudeLogRepository;
 
 class MinisterioSaudeServiceProvider extends ServiceProvider
 {
@@ -22,6 +26,14 @@ class MinisterioSaudeServiceProvider extends ServiceProvider
             EnderecoLocalRepositoryInterface::class,
             EnderecoLocalRepository::class
         );
+        $this->app->bind(
+            StatusOperadorRepositoryInterface::class,
+            StatusOperadorRepository::class
+        );
+        $this->app->bind(
+            MinisterioSaudeLogRepositoryInterface::class,
+            MinisterioSaudeLogRepository::class
+        );
 
         // Registro dos services como singletons
         $this->app->singleton(MinisterioSaudeApiService::class);
diff --git a/app/Domain/MinisterioSaude/Repositories/Common/BaseAbastractRepository.php b/app/Domain/MinisterioSaude/Repositories/Common/BaseAbastractRepository.php
new file mode 100644
index 0000000..bd24dd6
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/Common/BaseAbastractRepository.php
@@ -0,0 +1,88 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\Common;
+
+use Illuminate\Database\Eloquent\Collection;
+use Illuminate\Database\Eloquent\Model;
+
+abstract class BaseAbastractRepository
+{
+    protected $model;
+
+    public function __construct(Model $model)
+    {
+        $this->model = $model;
+    }
+
+    abstract public function list(array $filtros): Collection;
+
+    public function get(int $id): Model
+    {
+        return $this->model->where('id', $id)->firstOrFail();
+    }
+
+    public function getOneWhere(callable $where): ?Model
+    {
+        return $this->model->where($where)->first();
+    }
+
+    public function getWhere(callable $where): Collection
+    {
+        return $this->model->where($where)->get();
+    }
+
+    public function findBy(array $conditions): Model
+    {
+        $query = $this->model->newQuery();
+
+        foreach ($conditions as $field => $value) {
+            if (is_array($value)) {
+                $query->whereIn($field, $value);
+            } else {
+                $query->where($field, $value);
+            }
+        }
+
+        return $query->firstOrFail();
+    }
+
+    public function store(array $data): Collection
+    {
+        return new Collection($this->model->create($data));
+    }
+
+    public function storeArray(array $data): Collection
+    {
+        return new Collection($this->model->create($data));
+    }
+
+    public function storeSingleData(array $data): Collection
+    {
+        return new Collection($this->model->firstOrCreate($data));
+    }
+
+    public function updateOrStore(array $attributes, ?array $values): Collection
+    {
+        return new Collection($this->model->updateOrCreate($attributes, $values));
+    }
+
+    public function update(array $dados, int $id): bool
+    {
+        return $this->model->findOrFail($id)->update($dados);
+    }
+    public function updateArray(array $data, int $id): bool
+    {
+        return $this->model->findOrFail($id)->update($data);
+    }
+
+    public function destroy(int $id): bool
+    {
+        return $this->model->findOrFail($id)->delete();
+    }
+
+    public function searchByCustomField(string $field, string $value): Collection
+    {
+        return $this->model->where($field, $value)->get();
+    }
+
+}
diff --git a/app/Domain/MinisterioSaude/Repositories/Common/BaseRepositoryInterface.php b/app/Domain/MinisterioSaude/Repositories/Common/BaseRepositoryInterface.php
new file mode 100644
index 0000000..24f769b
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/Common/BaseRepositoryInterface.php
@@ -0,0 +1,23 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\Common;
+
+use Illuminate\Database\Eloquent\Model;
+use Illuminate\Support\Collection;
+
+interface BaseRepositoryInterface
+{
+    public function list(array $filtros): Collection;
+    public function get(int $id): Model;
+    public function getWhere(callable $conditions): Collection;
+    public function getOneWhere(callable $conditions): ?Model;
+    public function findBy(array $conditions): Model;
+    public function store(array $request): Collection;
+    public function storeArray(array $data): Collection;
+    public function storeSingleData(array $data): Collection;
+    public function updateOrStore(array $attributes, ?array $values): Collection;
+    public function update(array $request, int $id): bool;
+    public function updateArray(array $data, int $id): bool;
+    public function destroy(int $id): bool;
+    public function searchByCustomField(string $field, string $value): Collection;
+}
diff --git a/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/MinisterioSaudeLogRepositoryInterface.php b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/MinisterioSaudeLogRepositoryInterface.php
new file mode 100644
index 0000000..2b055c2
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/MinisterioSaudeLogRepositoryInterface.php
@@ -0,0 +1,20 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts;
+
+use Domain\MinisterioSaude\Repositories\Common\BaseRepositoryInterface;
+use Illuminate\Database\Eloquent\Collection;
+
+interface MinisterioSaudeLogRepositoryInterface extends BaseRepositoryInterface
+{
+    public function storeInserirStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeConsultarStatus($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeConsultaEndereco($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeConsultarItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeConsultarFaturas($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeAtualizarStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeConsultarPedidosFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeAtualizarPedidoFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeReceberFaturasFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+    public function storeObterPrecoMedioItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
+}
diff --git a/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/StatusOperadorRepositoryInterface.php b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/StatusOperadorRepositoryInterface.php
new file mode 100644
index 0000000..dfb4a5c
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/Contracts/StatusOperadorRepositoryInterface.php
@@ -0,0 +1,9 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts;
+
+use Domain\MinisterioSaude\Repositories\Common\BaseRepositoryInterface;
+
+interface StatusOperadorRepositoryInterface extends BaseRepositoryInterface
+{
+}
diff --git a/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/MinisterioSaudeLogRepository.php b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/MinisterioSaudeLogRepository.php
new file mode 100644
index 0000000..8923d94
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/MinisterioSaudeLogRepository.php
@@ -0,0 +1,184 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;
+
+use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
+use Illuminate\Database\Eloquent\Collection;
+
+class MinisterioSaudeLogRepository extends BaseAbastractRepository implements MinisterioSaudeLogRepositoryInterface
+{
+    protected $model;
+
+    public function __construct(MinisterioSaudeLog $model)
+    {
+        parent::__construct($model);
+    }
+
+    public function list(array $filtros): Collection
+    {
+        return $this->model->get();
+    }
+
+    // API 1.1 - Inserir Status Fatura
+    public function storeInserirStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'inserir_status_fatura',
+            '/status/criar',
+            'POST',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 1.2 - Consultar Status
+    public function storeConsultarStatus($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'consultar_status',
+            '/status/consultar',
+            'GET',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 1.3 - Consultar Endereço Local
+    public function storeConsultaEndereco($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'consultar_endereco',
+            '/endereco/consultar',
+            'GET',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+
+    }
+
+    // API 1.4 - Consultar Itens
+    public function storeConsultarItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'consultar_itens',
+            '/itens/consultar',
+            'POST',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 2.1 - Consultar Faturas
+    public function storeConsultarFaturas($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'consultar_faturas',
+            '/faturas/consultar',
+            'GET',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 2.2 - Atualizar Status Fatura
+    public function storeAtualizarStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'atualizar_status_fatura',
+            '/faturas/status/atualizar',
+            'PUT',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 3.1 - Consultar Pedidos Farmanet
+    public function storeConsultarPedidosFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'consultar_pedidos_farmanet',
+            '/pedidos-farmanet/consultar',
+            'GET',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 3.2 - Atualizar Pedido Farmanet
+    public function storeAtualizarPedidoFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'atualizar_pedido_farmanet',
+            '/pedidos-farmanet/atualizar',
+            'PUT',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 3.3 - Recebimento Faturas Farmanet
+    public function storeReceberFaturasFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'receber_faturas_farmanet',
+            '/faturas-farmanet/receber',
+            'PUT',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    // API 3.4 - Obter Preço Médio de Itens
+    public function storeObterPrecoMedioItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return $this->storeLog(
+            'obter_preco_medio_itens',
+            '/preco-medio-itens/obter',
+            'GET',
+            $requestData,
+            $responseData,
+            $sucesso,
+            $erro
+        );
+    }
+
+    public function storeLog($operacao, $endpoint, $metodo_http, $requestData, $responseData = null, $sucesso = false, $erro = null): Collection
+    {
+        return new Collection($this->model->create([
+            'operacao' => $operacao,
+            'endpoint' => $endpoint,
+            'metodo_http' => $metodo_http,
+            'request_data' => $requestData,
+            'response_data' => $responseData,
+            'status_code' => $responseData['status_code'] ?? 0,
+            'request_token' => $responseData['RequestToken'] ?? null,
+            'result_code' => $responseData['ResultCode'] ?? null,
+            'message' => $responseData['Message'] ?? null,
+            'dt_start' => $responseData['DtStart'] ?? null,
+            'dt_end' => $responseData['DtEnd'] ?? null,
+            'sucesso' => $sucesso,
+            'erro_message' => $erro,
+            'ip_address' => request()->ip()
+        ]));
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/StatusOperadorRepository.php b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/StatusOperadorRepository.php
new file mode 100644
index 0000000..0d5e720
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Repositories/FaturaFarmanet/StatusOperadorRepository.php
@@ -0,0 +1,23 @@
+<?php
+
+namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;
+
+use Domain\MinisterioSaude\Models\StatusOperador;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
+use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
+use Illuminate\Database\Eloquent\Collection;
+
+class StatusOperadorRepository extends BaseAbastractRepository implements StatusOperadorRepositoryInterface
+{
+    protected $model;
+
+    public function __construct(StatusOperador $model)
+    {
+        parent::__construct($model);
+    }
+
+    public function list(array $filtros): Collection
+    {
+        return $this->model->get();
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/AtualizarStatusFaturaRequest.php b/app/Domain/MinisterioSaude/Requests/AtualizarStatusFaturaRequest.php
deleted file mode 100644
index d3c770a..0000000
--- a/app/Domain/MinisterioSaude/Requests/AtualizarStatusFaturaRequest.php
+++ /dev/null
@@ -1,114 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Requests;
-
-use Illuminate\Foundation\Http\FormRequest;
-
-class AtualizarStatusFaturaRequest extends FormRequest
-{
-    /**
-     * Determine if the user is authorized to make this request.
-     *
-     * @return bool
-     */
-    public function authorize()
-    {
-        return true;
-    }
-
-    /**
-     * Get the validation rules that apply to the request.
-     *
-     * @return array
-     */
-    public function rules()
-    {
-        $requestData = $this->all();
-        
-        // Detectar formato da requisição
-        if (isset($requestData['Data'])) {
-            // Formato original: {"Data": {...}}
-            return [
-                'Data' => 'required|array',
-                'Data.ProtocoloIdGsnet' => 'required|string|max:40',
-                'Data.StatusCodigo' => 'required|string|max:10',
-                'Data.StatusDescricao' => 'required|string|max:100',
-                'Data.Observacao' => 'nullable|string',
-                'Data.DataStatus' => 'required|date_format:Y-m-d H:i:s',
-                'AccessToken' => 'nullable|string|max:240'
-            ];
-        } else {
-            // Formato novo: {"id_gestor": "...", "numero_fatura": "...", etc}
-            return [
-                'id_gestor' => 'required|string|max:50',
-                'numero_fatura' => 'required|string|max:100',
-                'status_fatura' => 'required|integer|min:1|max:10',
-                'data_atualizacao' => 'required|date_format:Y-m-d H:i:s',
-                'access_token' => 'required|string|max:240',
-                'observacoes' => 'nullable|string|max:500'
-            ];
-        }
-    }
-
-    /**
-     * Get custom error messages for validator errors.
-     *
-     * @return array
-     */
-    public function messages()
-    {
-        $requestData = $this->all();
-        
-        if (isset($requestData['Data'])) {
-            // Mensagens para formato original
-            return [
-                'Data.required' => 'Os dados são obrigatórios',
-                'Data.array' => 'Os dados devem estar em formato de array',
-                'Data.ProtocoloIdGsnet.required' => 'O protocolo ID GSNET é obrigatório',
-                'Data.ProtocoloIdGsnet.string' => 'O protocolo ID GSNET deve ser uma string',
-                'Data.ProtocoloIdGsnet.max' => 'O protocolo ID GSNET não pode ter mais de 40 caracteres',
-                'Data.StatusCodigo.required' => 'O código do status é obrigatório',
-                'Data.StatusCodigo.string' => 'O código do status deve ser uma string',
-                'Data.StatusCodigo.max' => 'O código do status não pode ter mais de 10 caracteres',
-                'Data.StatusDescricao.required' => 'A descrição do status é obrigatória',
-                'Data.StatusDescricao.string' => 'A descrição do status deve ser uma string',
-                'Data.StatusDescricao.max' => 'A descrição do status não pode ter mais de 100 caracteres',
-                'Data.Observacao.string' => 'A observação deve ser uma string',
-                'Data.DataStatus.required' => 'A data do status é obrigatória',
-                'Data.DataStatus.date_format' => 'A data do status deve estar no formato Y-m-d H:i:s',
-                'AccessToken.max' => 'O token de acesso deve ter no máximo 240 caracteres'
-            ];
-        } else {
-            // Mensagens para formato novo
-            return [
-                'id_gestor.required' => 'ID do gestor é obrigatório',
-                'id_gestor.max' => 'ID do gestor deve ter no máximo 50 caracteres',
-                'numero_fatura.required' => 'Número da fatura é obrigatório',
-                'numero_fatura.max' => 'Número da fatura deve ter no máximo 100 caracteres',
-                'status_fatura.required' => 'Status da fatura é obrigatório',
-                'status_fatura.integer' => 'Status da fatura deve ser um número inteiro',
-                'status_fatura.min' => 'Status da fatura deve ser maior que 0',
-                'status_fatura.max' => 'Status da fatura deve ser menor ou igual a 10',
-                'data_atualizacao.required' => 'Data de atualização é obrigatória',
-                'data_atualizacao.date_format' => 'Data de atualização deve estar no formato Y-m-d H:i:s',
-                'access_token.required' => 'Token de acesso é obrigatório',
-                'access_token.max' => 'Token de acesso deve ter no máximo 240 caracteres',
-                'observacoes.max' => 'Observações devem ter no máximo 500 caracteres'
-            ];
-        }
-    }
-
-    /**
-     * Handle a failed validation attempt.
-     */
-    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
-    {
-        $response = response()->json([
-            'success' => false,
-            'message' => 'Dados de atualização inválidos',
-            'errors' => $validator->errors()
-        ], 422);
-
-        throw new \Illuminate\Validation\ValidationException($validator, $response);
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Requests/Common/ApiFormRequest.php b/app/Domain/MinisterioSaude/Requests/Common/ApiFormRequest.php
new file mode 100644
index 0000000..efb2e56
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/Common/ApiFormRequest.php
@@ -0,0 +1,22 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\Common;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Illuminate\Foundation\Http\FormRequest;
+use Illuminate\Http\Exceptions\HttpResponseException;
+
+class ApiFormRequest extends FormRequest
+{
+    public function authorize(): bool
+    {
+        return true;
+    }
+
+    public function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
+    {
+        throw new HttpResponseException(
+            ServiceResponse::validationError($validator->errors()->toArray())->toErrorResponse()
+        );
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/ConsultarPedidosFarmanetRequest.php b/app/Domain/MinisterioSaude/Requests/ConsultarPedidosFarmanetRequest.php
index 7ded145..d274f4b 100644
--- a/app/Domain/MinisterioSaude/Requests/ConsultarPedidosFarmanetRequest.php
+++ b/app/Domain/MinisterioSaude/Requests/ConsultarPedidosFarmanetRequest.php
@@ -29,7 +29,6 @@ class ConsultarPedidosFarmanetRequest extends FormRequest
             'ano_referencia' => 'required|integer|digits:4|min:2020|max:2030',
             'mes_referencia' => 'nullable|integer|min:1|max:12',
             'ano_periodo_referencia' => 'nullable|integer|digits:4|min:2020|max:2030',
-            'access_token' => 'required|string|max:40',
         ];
     }
 
diff --git a/app/Domain/MinisterioSaude/Requests/ConsultarStatusOpRequest.php b/app/Domain/MinisterioSaude/Requests/ConsultarStatusOpRequest.php
deleted file mode 100644
index 7f964aa..0000000
--- a/app/Domain/MinisterioSaude/Requests/ConsultarStatusOpRequest.php
+++ /dev/null
@@ -1,53 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Requests;
-
-use Illuminate\Foundation\Http\FormRequest;
-
-class ConsultarStatusOpRequest extends FormRequest
-{
-    /**
-     * Determine if the user is authorized to make this request.
-     */
-    public function authorize(): bool
-    {
-        return true;
-    }
-
-    /**
-     * Get the validation rules that apply to the request.
-     */
-    public function rules(): array
-    {
-        return [
-            'IdStatus' => 'nullable|string|max:40',
-            'IdOrigem' => 'nullable|string|max:40', 
-            'NomeStatus' => 'nullable|string|max:40'
-        ];
-    }
-
-    /**
-     * Get custom messages for validator errors.
-     */
-    public function messages(): array
-    {
-        return [
-            'IdStatus.max' => 'IdStatus deve ter no máximo 40 caracteres',
-            'IdOrigem.max' => 'IdOrigem deve ter no máximo 40 caracteres',
-            'NomeStatus.max' => 'NomeStatus deve ter no máximo 40 caracteres'
-        ];
-    }
-
-    /**
-     * Get custom attributes for validator errors.
-     */
-    public function attributes(): array
-    {
-        return [
-            'AccessToken' => 'Token de Acesso',
-            'IdStatus' => 'ID do Status',
-            'IdOrigem' => 'ID de Origem',
-            'NomeStatus' => 'Nome do Status'
-        ];
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Requests/AtualizarPedidoFarmanetRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarPedidoFarmanetRequest.php
similarity index 95%
rename from app/Domain/MinisterioSaude/Requests/AtualizarPedidoFarmanetRequest.php
rename to app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarPedidoFarmanetRequest.php
index 53dd15c..a6fdc38 100644
--- a/app/Domain/MinisterioSaude/Requests/AtualizarPedidoFarmanetRequest.php
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarPedidoFarmanetRequest.php
@@ -1,6 +1,6 @@
 <?php
 
-namespace Domain\MinisterioSaude\Requests;
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
 
 use Illuminate\Foundation\Http\FormRequest;
 
@@ -44,21 +44,21 @@ class AtualizarPedidoFarmanetRequest extends FormRequest
             'id_gestor.required' => 'O ID do gestor é obrigatório',
             'id_gestor.string' => 'O ID do gestor deve ser uma string',
             'id_gestor.max' => 'O ID do gestor não pode ter mais de 22 caracteres',
-            
+
             'id_pedido_ms.required' => 'O ID do pedido MS é obrigatório',
             'id_pedido_ms.string' => 'O ID do pedido MS deve ser uma string',
             'id_pedido_ms.max' => 'O ID do pedido MS não pode ter mais de 50 caracteres',
-            
+
             'status_pedido.required' => 'O status do pedido é obrigatório',
             'status_pedido.integer' => 'O status do pedido deve ser um número inteiro',
             'status_pedido.in' => 'O status deve ser: 1 (Pendente), 2 (Em Processamento), 3 (Aprovado), 4 (Rejeitado), 5 (Cancelado)',
-            
+
             'observacoes.string' => 'As observações devem ser uma string',
             'observacoes.max' => 'As observações não podem ter mais de 1000 caracteres',
-            
+
             'data_atualizacao.required' => 'A data de atualização é obrigatória',
             'data_atualizacao.date_format' => 'A data de atualização deve estar no formato Y-m-d H:i:s',
-            
+
             'access_token.required' => 'O token de acesso é obrigatório',
             'access_token.string' => 'O token de acesso deve ser uma string',
             'access_token.max' => 'O token de acesso não pode ter mais de 40 caracteres',
diff --git a/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarStatusFaturaRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarStatusFaturaRequest.php
new file mode 100644
index 0000000..c20ab38
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/AtualizarStatusFaturaRequest.php
@@ -0,0 +1,46 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class AtualizarStatusFaturaRequest extends ApiFormRequest
+{
+    /**
+     * Get the validation rules that apply to the request.
+     *
+     * @return array
+     */
+    public function rules()
+    {
+        return [
+            'protocolo_id_gsnet' => 'required|integer|max:16',
+            'nr_documento' => 'required|integer|max:100',
+            'id_origem' => 'required|string|min:1|max:16',
+            'justificativa' => 'nullable|string|max:255'
+        ];
+    }
+
+    /**
+     * Get custom error messages for validator errors.
+     *
+     * @return array
+     */
+    public function messages()
+    {
+        return [
+            'protocolo_id_gsnet.required' => 'O protocolo ID GSNET é obrigatório',
+            'protocolo_id_gsnet.integer' => 'O protocolo ID GSNET deve ser um número inteiro',
+            'protocolo_id_gsnet.max' => 'O protocolo ID GSNET deve ter no máximo 16 caracteres',
+            'nr_documento.required' => 'O número do documento é obrigatório',
+            'nr_documento.integer' => 'O número do documento deve ser uma  número inteiro',
+            'nr_documento.max' => 'O número do documento deve ter no máximo 100 caracteres',
+            'id_origem.required' => 'O ID de origem é obrigatório',
+            'id_origem.string' => 'O ID de origem deve ser uma string',
+            'id_origem.max' => 'O ID de origem deve ser menor ou igual a 16',
+            'id_origem.min' => 'O ID de origem deve ser maior ou igual a 1',
+            'justificativa.string' => 'A justificativa deve ser uma string',
+            'justificativa.max' => 'A justificativa deve ter no máximo 255 caracteres'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/ConsultarEnderecoRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarEnderecoRequest.php
similarity index 58%
rename from app/Domain/MinisterioSaude/Requests/ConsultarEnderecoRequest.php
rename to app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarEnderecoRequest.php
index 2034055..1b6c353 100644
--- a/app/Domain/MinisterioSaude/Requests/ConsultarEnderecoRequest.php
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarEnderecoRequest.php
@@ -1,19 +1,11 @@
 <?php
 
-namespace Domain\MinisterioSaude\Requests;
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
 
-use Illuminate\Foundation\Http\FormRequest;
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
 
-class ConsultarEnderecoRequest extends FormRequest
+class ConsultarEnderecoRequest extends ApiFormRequest
 {
-    /**
-     * Determine if the user is authorized to make this request.
-     */
-    public function authorize(): bool
-    {
-        return true;
-    }
-
     /**
      * Get the validation rules that apply to the request.
      */
@@ -21,11 +13,9 @@ class ConsultarEnderecoRequest extends FormRequest
     {
         return [
             'IdGestor' => 'required_without:id_gestor|string|max:4',
-            'id_gestor' => 'required_without:IdGestor|string|max:4', 
+            'id_gestor' => 'required_without:IdGestor|string|max:4',
             'IdLocal' => 'required_without:id_local|string|max:5',
             'id_local' => 'required_without:IdLocal|string|max:5',
-            'AccessToken' => 'nullable|string|max:240',
-            'access_token' => 'nullable|string|max:240'
         ];
     }
 
@@ -36,23 +26,14 @@ class ConsultarEnderecoRequest extends FormRequest
     {
         // Normalizar parâmetros: usar versão PascalCase se disponível
         $data = $this->all();
-        
+
         if (isset($data['id_gestor']) && !isset($data['IdGestor'])) {
             $this->merge(['IdGestor' => $data['id_gestor']]);
         }
-        
+
         if (isset($data['id_local']) && !isset($data['IdLocal'])) {
             $this->merge(['IdLocal' => $data['id_local']]);
         }
-        
-        if (isset($data['access_token']) && !isset($data['AccessToken'])) {
-            $this->merge(['AccessToken' => $data['access_token']]);
-        }
-        
-        // Se não há AccessToken nos parâmetros, usar um padrão ou deixar vazio
-        if (!isset($data['AccessToken']) && !isset($data['access_token'])) {
-            $this->merge(['AccessToken' => 'default-token']);
-        }
     }
 
     /**
@@ -73,8 +54,6 @@ class ConsultarEnderecoRequest extends FormRequest
             'id_local.required_without' => 'id_local ou IdLocal é obrigatório',
             'id_local.string' => 'id_local deve ser uma string',
             'id_local.max' => 'id_local deve ter no máximo 5 caracteres',
-            'AccessToken.max' => 'AccessToken deve ter no máximo 240 caracteres',
-            'access_token.max' => 'access_token deve ter no máximo 240 caracteres'
         ];
     }
 
@@ -85,22 +64,7 @@ class ConsultarEnderecoRequest extends FormRequest
     {
         return [
             'IdGestor' => 'ID do Gestor',
-            'IdLocal' => 'ID do Local',
-            'AccessToken' => 'Token de Acesso'
+            'IdLocal' => 'ID do Local'
         ];
     }
-
-    /**
-     * Handle a failed validation attempt.
-     */
-    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
-    {
-        $response = response()->json([
-            'success' => false,
-            'message' => 'Parâmetros de consulta inválidos',
-            'errors' => $validator->errors()
-        ], 422);
-
-        throw new \Illuminate\Validation\ValidationException($validator, $response);
-    }
 }
diff --git a/app/Domain/MinisterioSaude/Requests/ConsultarFaturasRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarFaturasRequest.php
similarity index 72%
rename from app/Domain/MinisterioSaude/Requests/ConsultarFaturasRequest.php
rename to app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarFaturasRequest.php
index 4e2cac6..90f748a 100644
--- a/app/Domain/MinisterioSaude/Requests/ConsultarFaturasRequest.php
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarFaturasRequest.php
@@ -1,21 +1,11 @@
 <?php
 
-namespace Domain\MinisterioSaude\Requests;
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
 
-use Illuminate\Foundation\Http\FormRequest;
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
 
-class ConsultarFaturasRequest extends FormRequest
+class ConsultarFaturasRequest extends ApiFormRequest
 {
-    /**
-     * Determine if the user is authorized to make this request.
-     *
-     * @return bool
-     */
-    public function authorize()
-    {
-        return true;
-    }
-
     /**
      * Get the validation rules that apply to the request.
      *
@@ -28,10 +18,9 @@ class ConsultarFaturasRequest extends FormRequest
             'ano_referencia' => 'required|integer|min:2000|max:2050',
             'mes_referencia' => 'required|integer|min:1|max:12',
             'codigo_programa' => 'required|integer|min:1',
-            'access_token' => 'required|string|max:240',
+            'data_inicio' => 'required|date_format:Y-m-d',
+            'data_fim' => 'required|date_format:Y-m-d|after_or_equal:data_inicio',
             // Campos opcionais para filtros adicionais
-            'data_inicio' => 'nullable|date_format:Y-m-d',
-            'data_fim' => 'nullable|date_format:Y-m-d',
             'local_origem_id' => 'nullable|integer',
             'local_destino_id' => 'nullable|integer',
             'status' => 'nullable|string|max:10',
@@ -62,10 +51,11 @@ class ConsultarFaturasRequest extends FormRequest
             'codigo_programa.required' => 'O código do programa é obrigatório',
             'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
             'codigo_programa.min' => 'O código do programa deve ser maior que 0',
-            'access_token.required' => 'O token de acesso é obrigatório',
-            'access_token.max' => 'O token de acesso não pode ter mais de 240 caracteres',
             'data_inicio.date_format' => 'A data de início deve estar no formato Y-m-d',
+            'data_inicio.required' => 'A data de início é obrigatória',
             'data_fim.date_format' => 'A data de fim deve estar no formato Y-m-d',
+            'data_fim.required' => 'A data de fim é obrigatória',
+            'data_fim.after_or_equal' => 'A data de fim deve ser posterior ou igual à data de início',
             'local_origem_id.integer' => 'O ID do local de origem deve ser um número inteiro',
             'local_destino_id.integer' => 'O ID do local de destino deve ser um número inteiro',
             'status.string' => 'O status deve ser uma string',
@@ -77,18 +67,4 @@ class ConsultarFaturasRequest extends FormRequest
             'per_page.max' => 'O número de itens por página deve ser no máximo 100'
         ];
     }
-
-    /**
-     * Handle a failed validation attempt.
-     */
-    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
-    {
-        $response = response()->json([
-            'success' => false,
-            'message' => 'Parâmetros de consulta de faturas inválidos',
-            'errors' => $validator->errors()
-        ], 422);
-
-        throw new \Illuminate\Validation\ValidationException($validator, $response);
-    }
 }
diff --git a/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarStatusOpRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarStatusOpRequest.php
new file mode 100644
index 0000000..0d5bfcc
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ConsultarStatusOpRequest.php
@@ -0,0 +1,35 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class ConsultarStatusOpRequest extends ApiFormRequest
+{
+    public function rules(): array
+    {
+        return [
+            'id_status' => 'nullable|string|max:40',
+            'id_origem' => 'nullable|string|max:40',
+            'nome_status' => 'nullable|string|max:40',
+        ];
+    }
+
+    public function messages(): array
+    {
+        return [
+            'id_status.max' => 'id_status deve ter no máximo 40 caracteres',
+            'id_origem.max' => 'id_origem deve ter no máximo 40 caracteres',
+            'nome_status.max' => 'nome_status deve ter no máximo 40 caracteres'
+        ];
+    }
+
+    public function attributes(): array
+    {
+        return [
+            'id_status' => 'ID do Status',
+            'id_origem' => 'ID de Origem',
+            'nome_status' => 'Nome do Status'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/InserirStatusFaturaRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/InserirStatusFaturaRequest.php
new file mode 100644
index 0000000..7e4833a
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/InserirStatusFaturaRequest.php
@@ -0,0 +1,49 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class InserirStatusFaturaRequest extends ApiFormRequest
+{
+    /**
+     * Get the validation rules that apply to the request.
+     */
+    public function rules(): array
+    {
+        return [
+            'id_origem' => 'required|string|max:11',
+            'nome_status' => 'required|string|max:40',
+            'descricao_status' => 'required|string|max:240',
+            'system_code' => 'nullable|string'
+        ];
+    }
+
+    /**
+     * Get custom messages for validator errors.
+     */
+    public function messages(): array
+    {
+        return [
+            'id_origem.required' => 'IdOrigem não informado',
+            'id_origem.max' => 'IdOrigem deve ter no máximo 11 caracteres',
+            'nome_status.required' => 'NomeStatus não informado',
+            'nome_status.max' => 'NomeStatus deve ter no máximo 40 caracteres',
+            'descricao_status.required' => 'DescricaoStatus não informado',
+            'descricao_status.max' => 'DescricaoStatus deve ter no máximo 240 caracteres',
+        ];
+    }
+
+    /**
+     * Get custom attributes for validator errors.
+     */
+    public function attributes(): array
+    {
+        return [
+            'id_origem' => 'ID de Origem',
+            'nome_status' => 'Nome do Status',
+            'descricao_status' => 'Descrição do Status',
+            'system_code' => 'Código do Sistema'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ReceberFaturasRequest.php b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ReceberFaturasRequest.php
new file mode 100644
index 0000000..329588e
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/FaturaFarmamet/ReceberFaturasRequest.php
@@ -0,0 +1,82 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class ReceberFaturasRequest extends ApiFormRequest
+{
+    /**
+     * Get the validation rules that apply to the request.
+     *
+     * @return array
+     */
+    public function rules()
+    {
+        return [
+            'dt_documento' => 'nullable|date_format:d/m/Y|max:50',
+            'nr_documento' => 'nullable|string|max:100',
+            'vl_documento' => 'nullable|string|max:50',
+            'id_programa_saude' => 'nullable|string|max:50',
+            'id_local_destino' => 'nullable|string|max:50',
+            'cd_pedido' => 'nullable|string|max:100',
+            'itens' => 'nullable|array|min:1',
+            'itens.*.codigo_item' => 'nullable|string|max:100',
+            'itens.*.quantidade' => 'nullable|string|max:50',
+            'system_code' => 'nullable|string|max:100'
+        ];
+    }
+
+    /**
+     * Get custom messages for validator errors.
+     *
+     * @return array
+     */
+    public function messages()
+    {
+        return [
+            'dt_documento.date_format' => 'A data do documento deve estar no formato d/m/Y',
+            'dt_documento.max' => 'A data do documento deve ter no máximo 50 caracteres',
+            'nr_documento.string' => 'O número do documento deve ser uma string',
+            'nr_documento.max' => 'O número do documento deve ter no máximo 100 caracteres',
+            'vl_documento.string' => 'O valor do documento deve ser uma string',
+            'vl_documento.max' => 'O valor do documento deve ter no máximo 50 caracteres',
+            'id_programa_saude.string' => 'O ID do programa de saúde deve ser uma string',
+            'id_programa_saude.max' => 'O ID do programa de saúde deve ter no máximo 50 caracteres',
+            'id_local_destino.string' => 'O ID do local de destino deve ser uma string',
+            'id_local_destino.max' => 'O ID do local de destino deve ter no máximo 50 caracteres',
+            'cd_pedido.string' => 'O código do pedido deve ser uma string',
+            'cd_pedido.max' => 'O código do pedido deve ter no máximo 100 caracteres',
+            'itens.min' => 'Deve haver pelo menos um item',
+            'itens.*.codigo_item.required' => 'O código do item é obrigatório',
+            'itens.*.codigo_item.string' => 'O código do item deve ser uma string',
+            'itens.*.codigo_item.max' => 'O código do item deve ter no máximo 100 caracteres',
+            'itens.*.quantidade.required' => 'A quantidade do item é obrigatória',
+            'itens.*.quantidade.string' => 'A quantidade do item deve ser uma string',
+            'itens.*.quantidade.max' => 'A quantidade do item deve ter no máximo 50 caracteres',
+            'system_code.string' => 'O código do sistema deve ser uma string',
+            'system_code.max' => 'O código do sistema deve ter no máximo 100 caracteres'
+        ];
+    }
+
+    /**
+     * Get custom attributes for validator errors.
+     *
+     * @return array
+     */
+    public function attributes()
+    {
+        return [
+            'dt_documento' => 'Data do Documento',
+            'nr_documento' => 'Número do Documento',
+            'vl_documento' => 'Valor do Documento',
+            'id_programa_saude' => 'ID do Programa de Saúde',
+            'id_local_destino' => 'ID do Local de Destino',
+            'cd_pedido' => 'Código do Pedido',
+            'itens' => 'Itens',
+            'itens.*.codigo_item' => 'Código do Item',
+            'itens.*.quantidade' => 'Quantidade do Item',
+            'system_code' => 'Código do Sistema'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/InserirStatusFaturaRequest.php b/app/Domain/MinisterioSaude/Requests/InserirStatusFaturaRequest.php
deleted file mode 100644
index ee08b0a..0000000
--- a/app/Domain/MinisterioSaude/Requests/InserirStatusFaturaRequest.php
+++ /dev/null
@@ -1,61 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Requests;
-
-use Illuminate\Foundation\Http\FormRequest;
-
-class InserirStatusFaturaRequest extends FormRequest
-{
-    /**
-     * Determine if the user is authorized to make this request.
-     */
-    public function authorize(): bool
-    {
-        return true;
-    }
-
-    /**
-     * Get the validation rules that apply to the request.
-     */
-    public function rules(): array
-    {
-        return [
-            'Data.IdOrigem' => 'required|string|max:11',
-            'Data.NomeStatus' => 'required|string|max:40',
-            'Data.DescricaoStatus' => 'required|string|max:240',
-            'AccessToken' => 'required|string|max:240',
-            'SystemCode' => 'nullable|string'
-        ];
-    }
-
-    /**
-     * Get custom messages for validator errors.
-     */
-    public function messages(): array
-    {
-        return [
-            'Data.IdOrigem.required' => 'IdOrigem não informado',
-            'Data.IdOrigem.max' => 'IdOrigem deve ter no máximo 11 caracteres',
-            'Data.NomeStatus.required' => 'NomeStatus não informado',
-            'Data.NomeStatus.max' => 'NomeStatus deve ter no máximo 40 caracteres',
-            'Data.DescricaoStatus.required' => 'DescricaoStatus não informado',
-            'Data.DescricaoStatus.max' => 'DescricaoStatus deve ter no máximo 240 caracteres',
-            'AccessToken.required' => 'AccessToken não informado',
-            'AccessToken.max' => 'AccessToken deve ter no máximo 240 caracteres'
-        ];
-    }
-
-    /**
-     * Get custom attributes for validator errors.
-     */
-    public function attributes(): array
-    {
-        return [
-            'Data.IdOrigem' => 'ID de Origem',
-            'Data.NomeStatus' => 'Nome do Status',
-            'Data.DescricaoStatus' => 'Descrição do Status',
-            'AccessToken' => 'Token de Acesso',
-            'SystemCode' => 'Código do Sistema'
-        ];
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Requests/Item/ConsultarItensRequest.php b/app/Domain/MinisterioSaude/Requests/Item/ConsultarItensRequest.php
new file mode 100644
index 0000000..541965d
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/Item/ConsultarItensRequest.php
@@ -0,0 +1,39 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\Item;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+use Illuminate\Http\Exceptions\HttpResponseException;
+
+class ConsultarItensRequest extends ApiFormRequest
+{
+    public function rules(): array
+    {
+        return [
+            'lista_itens' => 'required|array|min:1|max:100',
+            'lista_itens.*.codigo_material' => 'required|numeric|digits_between:1,11'
+        ];
+    }
+
+    public function messages(): array
+    {
+        return [
+            'lista_itens.required' => 'A lista de itens é obrigatória',
+            'lista_itens.array' => 'A lista de itens deve ser um array',
+            'lista_itens.min' => 'A lista de itens deve ter no mínimo 1 item',
+            'lista_itens.max' => 'A lista de itens deve ter no máximo 100 itens',
+            'lista_itens.*.codigo_material.required' => 'O código do material é obrigatório',
+            'lista_itens.*.codigo_material.numeric' => 'O código do material deve ser um número',
+            'lista_itens.*.codigo_material.digits_between' => 'O código do material deve ter entre 1 e 11 dígitos'
+        ];
+    }
+
+    public function attributes(): array
+    {
+        return [
+            'lista_itens' => 'Lista de Itens',
+            'lista_itens.*.codigo_material' => 'Código do Material'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/Item/ObterPrecoMedioItensRequest.php b/app/Domain/MinisterioSaude/Requests/Item/ObterPrecoMedioItensRequest.php
new file mode 100644
index 0000000..e55bb94
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/Item/ObterPrecoMedioItensRequest.php
@@ -0,0 +1,37 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\Item;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+use Illuminate\Http\Exceptions\HttpResponseException;
+
+class ObterPrecoMedioItensRequest extends ApiFormRequest
+{
+    public function rules(): array
+    {
+        return [
+            'lista_itens' => 'nullable|array|min:1|max:100',
+            'lista_itens.*.codigo_item' => 'nullable|numeric|digits_between:1,11'
+        ];
+    }
+
+    public function messages(): array
+    {
+        return [
+            'lista_itens.array' => 'A lista de itens deve ser um array',
+            'lista_itens.min' => 'A lista de itens deve ter no mínimo 1 item',
+            'lista_itens.max' => 'A lista de itens deve ter no máximo 100 itens',
+            'lista_itens.*.codigo_item.numeric' => 'O código do material deve ser um número',
+            'lista_itens.*.codigo_item.digits_between' => 'O código do material deve ter entre 1 e 11 dígitos'
+        ];
+    }
+
+    public function attributes(): array
+    {
+        return [
+            'lista_itens' => 'Lista de Itens',
+            'lista_itens.*.codigo_item' => 'Código do Item'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/ObterPrecoMedioItensRequest.php b/app/Domain/MinisterioSaude/Requests/ObterPrecoMedioItensRequest.php
deleted file mode 100644
index 4d39e0e..0000000
--- a/app/Domain/MinisterioSaude/Requests/ObterPrecoMedioItensRequest.php
+++ /dev/null
@@ -1,73 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Requests;
-
-use Illuminate\Foundation\Http\FormRequest;
-
-class ObterPrecoMedioItensRequest extends FormRequest
-{
-    /**
-     * Determine if the user is authorized to make this request.
-     *
-     * @return bool
-     */
-    public function authorize()
-    {
-        return true;
-    }
-
-    /**
-     * Get the validation rules that apply to the request.
-     *
-     * @return array
-     */
-    public function rules()
-    {
-        return [
-            'codigo_programa' => 'required|integer|in:5,24,25,28',
-            'ano_referencia' => 'required|integer|digits:4|min:2020|max:2030',
-            'mes_referencia' => 'nullable|integer|min:1|max:12',
-            'codigo_medicamento' => 'nullable|string|max:20',
-            'codigo_farmaco' => 'nullable|string|max:20',
-            'estado_origem' => 'nullable|string|size:2',
-            'access_token' => 'required|string|max:40',
-        ];
-    }
-
-    /**
-     * Get custom messages for validator errors.
-     *
-     * @return array
-     */
-    public function messages()
-    {
-        return [
-            'codigo_programa.required' => 'O código do programa é obrigatório',
-            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
-            'codigo_programa.in' => 'O código do programa deve ser 5 (Diabetes), 24 (Dose Certa), 25 (Saúde da Mulher) ou 28 (Arboviroses)',
-            
-            'ano_referencia.required' => 'O ano de referência é obrigatório',
-            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
-            'ano_referencia.digits' => 'O ano de referência deve ter 4 dígitos',
-            'ano_referencia.min' => 'O ano de referência deve ser maior que 2019',
-            'ano_referencia.max' => 'O ano de referência deve ser menor que 2031',
-            
-            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
-            'mes_referencia.min' => 'O mês de referência deve ser entre 1 e 12',
-            'mes_referencia.max' => 'O mês de referência deve ser entre 1 e 12',
-            
-            'codigo_medicamento.string' => 'O código do medicamento deve ser uma string',
-            'codigo_medicamento.max' => 'O código do medicamento não pode ter mais de 20 caracteres',
-            
-            'codigo_farmaco.string' => 'O código do fármaco deve ser uma string',
-            'codigo_farmaco.max' => 'O código do fármaco não pode ter mais de 20 caracteres',
-            
-            'estado_origem.string' => 'O estado de origem deve ser uma string',
-            'estado_origem.size' => 'O estado de origem deve ter exatamente 2 caracteres',
-            
-            'access_token.required' => 'O token de acesso é obrigatório',
-            'access_token.string' => 'O token de acesso deve ser uma string',
-            'access_token.max' => 'O token de acesso não pode ter mais de 40 caracteres',
-        ];
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Requests/Planejamento/AtualizarPedidoRequest.php b/app/Domain/MinisterioSaude/Requests/Planejamento/AtualizarPedidoRequest.php
new file mode 100644
index 0000000..8c1644d
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/Planejamento/AtualizarPedidoRequest.php
@@ -0,0 +1,39 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\Planejamento;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class AtualizarPedidoRequest extends ApiFormRequest
+{
+    public function rules(): array
+    {
+        return [
+            'id_programa_saude' => 'required|integer',
+            'codigo_pedido' => 'required|integer',
+            'protocolo_operador' => 'required|string|max:50'
+        ];
+    }
+
+    public function messages(): array
+    {
+        return [
+            'id_programa_saude.required' => 'O ID do programa de saúde é obrigatório',
+            'id_programa_saude.integer' => 'O ID do programa de saúde deve ser um número inteiro',
+            'codigo_pedido.required' => 'O código do pedido é obrigatório',
+            'codigo_pedido.integer' => 'O código do pedido deve ser um número inteiro',
+            'protocolo_operador.required' => 'O protocolo do operador é obrigatório',
+            'protocolo_operador.string' => 'O protocolo do operador deve ser uma string',
+            'protocolo_operador.max' => 'O protocolo do operador deve ter no máximo 50 caracteres'
+        ];
+    }
+
+    public function attributes(): array
+    {
+        return [
+            'id_programa_saude' => 'ID do Programa de Saúde',
+            'codigo_pedido' => 'Código do Pedido',
+            'protocolo_operador' => 'Protocolo do Operador'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Requests/Planejamento/ConsultarPedidosRequest.php b/app/Domain/MinisterioSaude/Requests/Planejamento/ConsultarPedidosRequest.php
new file mode 100644
index 0000000..284114d
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Requests/Planejamento/ConsultarPedidosRequest.php
@@ -0,0 +1,50 @@
+<?php
+
+namespace Domain\MinisterioSaude\Requests\Planejamento;
+
+use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
+
+class ConsultarPedidosRequest extends ApiFormRequest
+{
+    public function rules(): array
+    {
+        return [
+            'codigo_programa' => 'nullable|integer',
+            'ano_referencia' => 'nullable|integer|digits:4|min:2000|max:2199',
+            'mes_referencia' => 'nullable|integer|min:1|max:12',
+            'ano_periodo_referencia' => 'nullable|integer|digits:4|min:2000|max:2199',
+            'id_gestor' => 'nullable|integer|max:22'
+        ];
+    }
+
+    public function messages(): array
+    {
+        return [
+            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
+            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
+            'ano_referencia.digits' => 'O ano de referência deve ter 4 dígitos',
+            'ano_referencia.min' => 'O ano de referência deve ser maior que 2000',
+            'ano_referencia.max' => 'O ano de referência deve ser menor que 2200',
+            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
+            'mes_referencia.min' => 'O mês de referência deve ser entre 1 e 12',
+            'mes_referencia.max' => 'O mês de referência deve ser entre 1 e 12',
+            'ano_periodo_referencia.integer' => 'O ano período de referência deve ser um número inteiro',
+            'ano_periodo_referencia.digits' => 'O ano período de referência deve ter 4 dígitos',
+            'ano_periodo_referencia.min' => 'O ano período de referência deve ser maior que 2000',
+            'ano_periodo_referencia.max' => 'O ano período de referência deve ser menor que 2200',
+            'id_gestor.integer' => 'O ID do gestor deve ser um número inteiro',
+            'id_gestor.max' => 'O ID do gestor deve ter no máximo 22 caracteres'
+        ];
+    }
+
+    public function attributes(): array
+    {
+        return [
+            'codigo_programa' => 'Código do Programa',
+            'ano_referencia' => 'Ano de Referência',
+            'mes_referencia' => 'Mês de Referência',
+            'ano_periodo_referencia' => 'Ano do Período de Referência',
+            'id_gestor' => 'ID do Gestor'
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/ConsultarItensService.php b/app/Domain/MinisterioSaude/Services/ConsultarItensService.php
deleted file mode 100644
index d3452ba..0000000
--- a/app/Domain/MinisterioSaude/Services/ConsultarItensService.php
+++ /dev/null
@@ -1,144 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Services;
-
-use App\Models\ItemMaterial;
-use GuzzleHttp\Client;
-use GuzzleHttp\Exception\RequestException;
-use Illuminate\Support\Facades\Log;
-use Carbon\Carbon;
-
-class ConsultarItensService
-{
-    private $client;
-    private $baseUrl;
-
-    public function __construct()
-    {
-        $this->client = new Client([
-            'timeout' => config('ministerio_saude.items.timeout', 30),
-            'verify' => false,
-        ]);
-        
-        $environment = config('ministerio_saude.items.environment', 'homolog');
-        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
-    }
-
-    /**
-     * Consulta itens no sistema GSNET do Ministério da Saúde
-     *
-     * @param array $data
-     * @return array
-     */
-    public function consultarItens(array $data): array
-    {
-        try {
-            $endpoint = config('ministerio_saude.items.endpoints.consultar_itens');
-            $url = $this->baseUrl . $endpoint . '?AccessToken=' . $data['AccessToken'];
-            
-            Log::info('ConsultarItensService - Iniciando consulta de itens via API real', [
-                'url' => $url,
-                'total_itens' => count($data['Data']['ListaItens']),
-                'codigos_materiais' => array_column($data['Data']['ListaItens'], 'CodigoMaterial')
-            ]);
-
-            // Preparar payload para o Ministério
-            $payload = [
-                'Data' => [
-                    'ListaItens' => $data['Data']['ListaItens']
-                ],
-                'AccessToken' => $data['AccessToken']
-            ];
-
-            // Header customizado necessário (usar o primeiro CodigoMaterial como exemplo)
-            $primeiroCodigoMaterial = $data['Data']['ListaItens'][0]['CodigoMaterial'] ?? '1160';
-
-            $response = $this->client->put($url, [
-                'headers' => [
-                    'Content-Type' => 'application/json',
-                    'Accept' => 'application/json',
-                    'CodigoMaterial' => (string)$primeiroCodigoMaterial, // Header customizado necessário
-                ],
-                'json' => $payload
-            ]);
-
-            $responseData = json_decode($response->getBody()->getContents(), true);
-            
-            Log::info('ConsultarItensService - Resposta recebida do Ministério', [
-                'status_code' => $response->getStatusCode(),
-                'total_itens_retornados' => count($responseData['Data'] ?? []),
-                'protocolo' => $responseData['Protocolo'] ?? null
-            ]);
-
-            return [
-                'success' => true,
-                'data' => $responseData,
-                'message' => 'Consulta de itens realizada com sucesso via API real do Ministério.',
-                'total_itens' => count($responseData['Data'] ?? []),
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-
-        } catch (RequestException $e) {
-            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
-            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
-            
-            Log::error('ConsultarItensService - Erro na requisição para o Ministério', [
-                'message' => $e->getMessage(),
-                'status_code' => $statusCode,
-                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
-                'request_data' => $data
-            ]);
-
-            return [
-                'success' => false,
-                'message' => $errorMessage,
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-
-        } catch (\Exception $e) {
-            Log::error('ConsultarItensService - Erro geral', [
-                'message' => $e->getMessage(),
-                'trace' => $e->getTraceAsString(),
-                'request_data' => $data
-            ]);
-
-            return [
-                'success' => false,
-                'message' => 'Erro ao consultar itens: ' . $e->getMessage(),
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-        }
-    }
-
-    /**
-     * Processa e armazena os itens recebidos do Ministério
-     *
-     * @param array $itens
-     * @return void
-     */
-    private function processarEArmazenarItens(array $itens): void
-    {
-        foreach ($itens as $item) {
-            try {
-                ItemMaterial::updateOrCreate(
-                    ['codigo_material' => $item['CodigoMaterial']],
-                    [
-                        'codigo_material' => $item['CodigoMaterial'],
-                        'nome_material' => $item['NomeMaterial'] ?? null,
-                        'nome_descricao_tecnica' => $item['NomeDescricaoTecnica'] ?? null,
-                        'nome_unidade_medida' => $item['NomeUnidadeMedida'] ?? null,
-                        'st_registro' => $item['StRegistro'] ?? null,
-                        'codigo_siafisico' => $item['CodigoSiafisico'] ?? null,
-                        'data_ultima_consulta' => Carbon::now(),
-                        'origem' => 'ministerio_saude_sp'
-                    ]
-                );
-            } catch (\Exception $e) {
-                Log::warning('ConsultarItensService - Erro ao armazenar item', [
-                    'codigo_material' => $item['CodigoMaterial'] ?? 'indefinido',
-                    'error' => $e->getMessage()
-                ]);
-            }
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Services/ConsultarPedidosFarmanetService.php b/app/Domain/MinisterioSaude/Services/ConsultarPedidosFarmanetService.php
index ca06aeb..c959274 100644
--- a/app/Domain/MinisterioSaude/Services/ConsultarPedidosFarmanetService.php
+++ b/app/Domain/MinisterioSaude/Services/ConsultarPedidosFarmanetService.php
@@ -20,7 +20,7 @@ class ConsultarPedidosFarmanetService
             'timeout' => config('ministerio_saude.farmanet.timeout', 30),
             'verify' => false,
         ]);
-        
+
         $environment = config('ministerio_saude.farmanet.environment', 'homolog');
         $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
     }
@@ -35,8 +35,9 @@ class ConsultarPedidosFarmanetService
     {
         try {
             $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_pedidos');
+            $access_token = config('ministerio_saude.api.access_token');
             $url = $this->baseUrl . $endpoint;
-            
+
             Log::info('ConsultarPedidosFarmanetService - Iniciando consulta', [
                 'url' => $url,
                 'params' => $params
@@ -44,35 +45,35 @@ class ConsultarPedidosFarmanetService
 
             $response = $this->client->get($url, [
                 'headers' => [
-                    'Authorization' => 'Bearer ' . $params['access_token'],
                     'Content-Type' => 'application/json',
                     'Accept' => 'application/json',
                 ],
                 'query' => [
-                    'id_gestor' => $params['id_gestor'],
-                    'codigo_programa' => $params['codigo_programa'] ?? null,
-                    'ano_referencia' => $params['ano_referencia'],
-                    'mes_referencia' => $params['mes_referencia'] ?? null,
-                    'ano_periodo_referencia' => $params['ano_periodo_referencia'] ?? null,
+                    'IdGestor' => $params['id_gestor'],
+                    'CodigoPrograma' => $params['codigo_programa'] ?? null,
+                    'AnoReferencia' => $params['ano_referencia'],
+                    'MesReferencia' => $params['mes_referencia'] ?? null,
+                    'AnoPeriodoReferencia' => $params['ano_periodo_referencia'] ?? null,
+                    'AccessToken' => $access_token
                 ]
             ]);
 
             $data = json_decode($response->getBody()->getContents(), true);
-            
+
             Log::info('ConsultarPedidosFarmanetService - Resposta recebida', [
                 'status_code' => $response->getStatusCode(),
-                'total_pedidos' => count($data['pedidos'] ?? [])
+                'total_pedidos' => count($data['Data'] ?? [])
             ]);
 
             // Processar e armazenar os pedidos localmente
-            if (isset($data['pedidos']) && is_array($data['pedidos'])) {
-                $this->processarEArmazenarPedidos($data['pedidos'], $params);
+            if (isset($data['Data']) && is_array($data['Data'])) {
+                $this->processarEArmazenarPedidos($data['Data'], $params);
             }
 
             return [
                 'success' => true,
                 'data' => $data,
-                'total_pedidos' => count($data['pedidos'] ?? []),
+                'total_pedidos' => count($data['Data'] ?? []),
                 'timestamp' => Carbon::now()->toISOString()
             ];
 
diff --git a/app/Domain/MinisterioSaude/Services/EnderecoLocalService.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/EnderecoLocalService.php
similarity index 62%
rename from app/Domain/MinisterioSaude/Services/EnderecoLocalService.php
rename to app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/EnderecoLocalService.php
index cd8a5c6..755b5d4 100644
--- a/app/Domain/MinisterioSaude/Services/EnderecoLocalService.php
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/EnderecoLocalService.php
@@ -1,35 +1,49 @@
 <?php
 
-namespace Domain\MinisterioSaude\Services;
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal;
 
-use Domain\MinisterioSaude\DTOs\ConsultarEnderecoDTO;
 use Domain\MinisterioSaude\DTOs\EnderecoResponseDTO;
 use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
 use Domain\MinisterioSaude\Repositories\EnderecoLocalRepositoryInterface;
-use Illuminate\Support\Facades\Http;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input\ConsultaEnderecoInput;
 use Illuminate\Support\Facades\Log;
 use GuzzleHttp\Client;
 use GuzzleHttp\Exception\RequestException;
+use Domain\MinisterioSaude\Traits\HasServiceResponse;
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use InvalidArgumentException;
+use Domain\MinisterioSaude\Services\GeolocalizacaoService;
+use Domain\MinisterioSaude\Exceptions\CustomMessageException;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
 
 class EnderecoLocalService
 {
+    use HasServiceResponse;
+    use HasLog;
+
     private EnderecoLocalRepositoryInterface $repository;
     private GeolocalizacaoService $geoService;
     private Client $client;
     private string $baseUrl;
+    private ?string $accessToken;
+    private MinisterioSaudeLogRepositoryInterface $logRepository;
 
     public function __construct(
         EnderecoLocalRepositoryInterface $repository,
-        GeolocalizacaoService $geoService
+        GeolocalizacaoService $geoService,
+        MinisterioSaudeLogRepositoryInterface $logRepository
     ) {
         $this->repository = $repository;
         $this->geoService = $geoService;
-        
+        $this->logRepository = $logRepository;
+
+        $this->accessToken = config('ministerio_saude.api.access_token');
         $this->client = new Client([
             'timeout' => config('ministerio_saude.api.timeout', 30),
             'verify' => false,
         ]);
-        
+
         $environment = config('ministerio_saude.api.environment', 'homolog');
         $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
     }
@@ -37,69 +51,34 @@ class EnderecoLocalService
     /**
      * Consultar endereço na API do Ministério da Saúde
      */
-    public function consultarEndereco(string $idGestor, string $idLocal): array
+    public function consultarEndereco(ConsultaEnderecoInput $input): ServiceResponse
     {
+        if ($this->accessToken === null) {
+            throw new InvalidArgumentException('Missing API configuration');
+        }
+
         try {
-            $dto = new ConsultarEnderecoDTO(
-                config('ministerio_saude.api.access_token'),
-                $idGestor,
-                $idLocal
-            );
+            $startTime = microtime(true);
 
-            if (!$dto->isValid()) {
-                return [
-                    'success' => false,
-                    'message' => 'Dados de entrada inválidos: ' . implode(', ', $dto->validate()),
-                    'data' => null
-                ];
+            if (!$input->isValid()) {
+                throw new CustomMessageException('Dados de entrada inválidos: ' . implode(', ', $input->getErrors()), null, "INVALID_INPUT", 400);
             }
 
-            $startTime = microtime(true);
-            
-            // NOTA: A API de endereço do Ministério pode não estar disponível
-            // Fazer chamada HTTP real para a API do Ministério usando GET
-            $url = $this->baseUrl . config('ministerio_saude.endpoints.consultar_endereco_local');
-            
-            $queryParams = [
-                'IdGestor' => $dto->idGestor,
-                'IdLocal' => $dto->idLocal,
-                'AccessToken' => config('ministerio_saude.api.access_token')
-            ];
-            
-            Log::info('EnderecoLocalService - Chamando API real do Ministério', [
+            $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.consultar_endereco_local');
+            $queryParams = $input->toQueryParams($this->accessToken);
+
+            $this->LogInfo('EnderecoLocalService - Chamando API real do Ministério', [
                 'url' => $url,
                 'params' => collect($queryParams)->except(['AccessToken'])->toArray()
             ]);
 
-            try {
-                $response = $this->client->get($url, [
-                    'query' => $queryParams,
-                    'headers' => [
-                        'Accept' => 'application/json',
-                    ]
-                ]);
-            } catch (RequestException $e) {
-                $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
-                
-                if ($statusCode === 404) {
-                    Log::warning('EnderecoLocalService - API de endereço não encontrada', [
-                        'url' => $url,
-                        'status_code' => $statusCode,
-                        'message' => 'Endpoint de endereço não está disponível na API do Ministério'
-                    ]);
-                    
-                    return [
-                        'success' => false,
-                        'message' => 'API de consulta de endereços não está disponível no Ministério da Saúde SP. Endpoint: ' . $url,
-                        'data' => null,
-                        'error_code' => 'ENDPOINT_NOT_FOUND'
-                    ];
-                }
-                
-                // Re-throw para outros tipos de erro
-                throw $e;
-            }
-            
+            $response = $this->client->get($url, [
+                'query' => $queryParams,
+                'headers' => [
+                    'Accept' => 'application/json',
+                ]
+            ]);
+
             $responseBody = json_decode($response->getBody()->getContents(), true);
             $endTime = microtime(true);
 
@@ -107,86 +86,84 @@ class EnderecoLocalService
 
             if ($response->getStatusCode() === 200 && !empty($responseBody['Data'])) {
                 $enderecoDTO = new EnderecoResponseDTO($responseBody['Data'][0] ?? []);
-                
-                Log::info('EnderecoLocalService - Resposta da API real recebida', [
+
+                $this->LogInfo('EnderecoLocalService - Resposta da API real recebida', [
                     'status_code' => $response->getStatusCode(),
                     'endereco_encontrado' => !empty($responseBody['Data'][0])
                 ]);
-                
-                // Remover geolocalização por enquanto - focar apenas na API real
-                // if (config('ministerio_saude.geolocalizacao.enabled')) {
-                //     dispatch(function() use ($enderecoDTO) {
-                //         $this->geoService->processarGeolocalizacao($enderecoDTO);
-                //     })->afterResponse();
-                // }
 
                 // Log de sucesso
-                MinisterioSaudeLog::consultarEndereco(
-                    $dto->toQueryParams(),
+                $this->logRepository->storeConsultaEndereco(
+                    $input->toArray(),
                     array_merge($responseBody, ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
                     true
                 );
 
-                return [
-                    'success' => true,
-                    'message' => 'Endereço consultado com sucesso via API real',
-                    'data' => [
-                        'endereco_api' => $enderecoDTO->toArray(),
-                        'tempo_resposta_ms' => $tempoResposta,
-                        'fonte' => 'API Real do Ministério da Saúde'
-                    ]
-                ];
-            } else {
-                // Log de erro
-                MinisterioSaudeLog::consultarEndereco(
-                    $dto->toQueryParams(),
-                    array_merge($responseBody ?? [], ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
-                    false,
-                    $responseBody['Message'] ?? 'Endereço não encontrado'
-                );
-
-                return [
-                    'success' => false,
-                    'message' => $responseBody['Message'] ?? 'Endereço não encontrado na API',
-                    'data' => $responseBody
-                ];
+                return $this->successResponse([
+                    'endereco_api' => $enderecoDTO->toArray(),
+                    'tempo_resposta_ms' => $tempoResposta,
+                    'fonte' => 'API Real do Ministério da Saúde'
+                ], 'Endereço consultado com sucesso via API real');
             }
 
+            // Log de erro
+            $this->logRepository->storeConsultaEndereco(
+                $input->toArray(),
+                array_merge($responseBody ?? [], ['status_code' => $response->getStatusCode(), 'tempo_resposta_ms' => $tempoResposta]),
+                false,
+                $responseBody['Message'] ?? 'Endereço não encontrado'
+            );
+
+            throw new CustomMessageException($responseBody['Message'] ?? 'Endereço não encontrado na API', $responseBody, "NOT_FOUND", 404);
+        } catch (CustomMessageException $e) {
+
+            $this->logError('EnderecoLocalService@consultarEndereco - Erro', $e, [
+                'id_gestor' => $input->idGestor,
+                'id_local' => $input->idLocal
+            ]);
+
+            $response = $this->failureResponse($e->getMessage(), $e->getData());
         } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+
+            $this->logRepository->storeConsultaEndereco(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            if ($statusCode === 404) {
+                $this->logWarning('EnderecoLocalService - API de endereço não encontrada', [
+                    'url' => $url,
+                    'status_code' => $statusCode,
+                    'message' => 'Endpoint de endereço não está disponível na API do Ministério'
+                ]);
+
+                return $this->notFoundResponse("API de consulta de endereços não está disponível no Ministério da Saúde SP. Endpoint: {$url}");
+            }
+
             $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
-            
-            Log::error('EnderecoLocalService - Erro na API externa', [
-                'error' => $errorMessage,
+            $this->logError('EnderecoLocalService - Erro na API externa', $e, [
                 'url' => $url ?? 'URL não definida'
             ]);
-            
-            return [
-                'success' => false,
-                'message' => $errorMessage
-            ];
+
+            $response = $this->failureResponse($errorMessage)->setErrorCode("FORBIDDEN");
 
         } catch (\Exception $e) {
-            Log::error('EnderecoLocalService@consultarEndereco - Erro', [
-                'id_gestor' => $idGestor,
-                'id_local' => $idLocal,
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
+            $this->logError('EnderecoLocalService@consultarEndereco - Erro', $e, $input->toArray());
 
             // Log de erro
-            MinisterioSaudeLog::consultarEndereco(
-                ['IdGestor' => $idGestor, 'IdLocal' => $idLocal],
+            $this->logRepository->storeConsultaEndereco(
+                $input->toArray(),
                 null,
                 false,
                 $e->getMessage()
             );
 
-            return [
-                'success' => false,
-                'message' => 'Erro interno do servidor',
-                'data' => null
-            ];
+            $response = $this->failureResponse('Erro interno do servidor', null)->setNullData();
         }
+        return $response;
     }
 
     /**
@@ -300,7 +277,7 @@ class EnderecoLocalService
     {
         try {
             $limite = $limite ?? config('ministerio_saude.geolocalizacao.batch_limit', 50);
-            
+
             if (!config('ministerio_saude.geolocalizacao.enabled')) {
                 return [
                     'success' => false,
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/Input/ConsultaEnderecoInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/Input/ConsultaEnderecoInput.php
new file mode 100644
index 0000000..1829c50
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/EnderecoLocal/Input/ConsultaEnderecoInput.php
@@ -0,0 +1,71 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\EnderecoLocal\Input;
+
+class ConsultaEnderecoInput
+{
+    public string $idGestor;
+    public string $idLocal;
+    private array $errors = [];
+
+    public function __construct(string $idGestor, string $idLocal)
+    {
+        $this->idGestor = $idGestor;
+        $this->idLocal  = $idLocal;
+    }
+
+    public static function fromArray(array $data): ConsultaEnderecoInput
+    {
+        return new self(
+            $data['IdGestor'],
+            $data['IdLocal']
+        );
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        return [
+            'IdGestor' => $this->idGestor,
+            'IdLocal' => $this->idLocal,
+            'AccessToken' => $accessToken
+        ];
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'IdGestor' => $this->idGestor,
+            'IdLocal' => $this->idLocal
+        ];
+    }
+
+    public function validate(): void
+    {
+        $errors = [];
+
+        if (empty($this->idGestor)) {
+            $errors[] = 'IdGestor não informado';
+        } elseif (strlen($this->idGestor) > 4) {
+            $errors[] = 'IdGestor deve ter no máximo 4 caracteres';
+        }
+
+        if (empty($this->idLocal)) {
+            $errors[] = 'IdLocal não informado';
+        } elseif (strlen($this->idLocal) > 5) {
+            $errors[] = 'IdLocal deve ter no máximo 5 caracteres';
+        }
+
+        $this->errors = $errors;
+    }
+
+    public function isValid(): bool
+    {
+        $this->validate();
+        return empty($this->errors);
+    }
+
+    public function getErrors(): array
+    {
+        return $this->errors;
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/FaturaGsnetService.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/FaturaGsnetService.php
new file mode 100644
index 0000000..67ef475
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/FaturaGsnetService.php
@@ -0,0 +1,334 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura;
+
+use App\Models\FaturaGsnet;
+use App\Models\FaturaGsnetItem;
+use Domain\MinisterioSaude\Exceptions\CustomMessageException;
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\AtualizarStatusFaturaInput;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Domain\MinisterioSaude\Traits\HasServiceResponse;
+use Illuminate\Support\Facades\DB;
+use GuzzleHttp\Client;
+use GuzzleHttp\Exception\RequestException;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ReceberFaturasInput;
+
+class FaturaGsnetService
+{
+    use HasServiceResponse;
+    use HasLog;
+
+    private Client $client;
+    private string $baseUrl;
+    private string $accessToken;
+    private MinisterioSaudeLogRepositoryInterface $logRepository;
+
+    public function __construct(
+        MinisterioSaudeLogRepositoryInterface $logRepository
+    ) {
+        $this->client = new Client([
+            'timeout' => config('ministerio_saude.api.timeout', 30),
+            'verify' => false,
+        ]);
+
+        $environment = config('ministerio_saude.api.environment', 'homolog');
+        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
+        $this->accessToken = config('ministerio_saude.api.access_token');
+        $this->logRepository = $logRepository;
+    }
+    /**
+     * API 2.1 - Consultar Faturas via API real do Ministério
+     *
+     * @param ConsultaFaturaInput $data
+     * @return array
+     */
+    public function consultarFaturas(ConsultaFaturaInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_faturas_gsnet');
+            $queryParams = $input->toQueryParams($this->accessToken);
+            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);
+
+            $this->logInfo('FaturaGsnetService - Consultando faturas via API real', [
+                'url' => $url,
+                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
+            ]);
+
+            $response = $this->client->get($url, [
+                'headers' => [
+                    'Accept' => 'application/json',
+                ]
+            ]);
+
+            $responseBody = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
+                'status_code' => $response->getStatusCode(),
+                'result_code' => $responseBody['ResultCode'] ?? null,
+                'message' => $responseBody['Message'] ?? null,
+                'total_faturas' => count($responseBody['Data'] ?? [])
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na consulta', $responseBody);
+            }
+
+            $this->logRepository->storeConsultarFaturas(
+                $input->toArray(),
+                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse($responseBody, 'Consulta de faturas realizada com sucesso via API real do Ministério.');
+        } catch (CustomMessageException $e) {
+
+            $this->logRepository->storeConsultarFaturas(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
+
+            $this->logError('FaturaGsnetService - Erro na requisição para o Ministério', $e, [
+                'status_code' => $statusCode,
+                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
+                'request_data' => $input->toArray()
+            ]);
+
+            $this->logRepository->storeConsultarFaturas(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (\Exception $e) {
+            $this->logError('FaturaGsnetService - Erro geral', $e, [
+                'request_data' => $input->toArray()
+            ]);
+
+            $this->logRepository->storeConsultarFaturas(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse('Erro interno: ' . $e->getMessage())->setNullData();
+        }
+        return $response;
+    }
+
+    /**
+     * API 2.2 - Atualizar Status da Fatura
+     *
+     * @param AtualizarStatusFaturaInput $input
+     * @return ServiceResponse
+     */
+    public function atualizarStatusFatura(AtualizarStatusFaturaInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.farmanet.endpoints.atualizar_status_fatura_gsnet');
+            $queryParams = $input->toQueryParams($this->accessToken);
+            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);
+            $this->logInfo('FaturaGsnetService - Atualizando status da fatura via API real', [
+                'url' => $url,
+                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
+            ]);
+            $response = $this->client->get($url, [
+                'headers' => [
+                    'Accept' => 'application/json',
+                ]
+            ]);
+            $responseBody = json_decode($response->getBody()->getContents(), true);
+            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
+                'status_code' => $response->getStatusCode(),
+                'result_code' => $responseBody['ResultCode'] ?? null,
+                'message' => $responseBody['Message'] ?? null
+            ]);
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na atualização', $responseBody);
+            }
+            $this->logRepository->storeAtualizarStatusFatura(
+                $input->toArray(),
+                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+            return $this->successResponse($responseBody, 'Status da fatura atualizado com sucesso');
+        } catch (CustomMessageException $e) {
+            $this->logRepository->storeAtualizarStatusFatura(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
+            $this->logError('FaturaGsnetService - Erro na atualização do status da fatura', $e, [
+                'url' => $url ?? 'URL não definida',
+                'data' => $input->toArray() ?? []
+            ]);
+
+            $this->logRepository->storeAtualizarStatusFatura(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (\Exception $e) {
+            $this->logError('FaturaGsnetService - Erro ao atualizar status da fatura', $e);
+
+            $this->logRepository->storeAtualizarStatusFatura(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response =  $this->failureResponse('Erro ao atualizar status da fatura: ' . $e->getMessage())->setNullData();
+        }
+        return $response;
+    }
+
+    /**
+     * API 3.3 - Receber Faturas Farmanet (Envio)
+     *
+     * @param ReceberFaturasInput $input
+     * @return ServiceResponse
+     */
+    public function receberFaturas(ReceberFaturasInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.farmanet.endpoints.receber_faturas');
+            $url = "{$this->baseUrl}{$endpoint}";
+
+            $queryParams = $input->toQueryParams($this->accessToken);
+            $this->logInfo('FaturaGsnetService - Enviando fatura para recebimento via API', [
+                'url' => $url,
+                'payload' => collect($queryParams)->except(['AccessToken'])->toArray()
+            ]);
+
+            $response = $this->client->put($url, [
+                'json' => $queryParams,
+                'headers' => [
+                    'Content-Type' => 'application/json',
+                    'Accept' => 'application/json',
+                ]
+            ]);
+
+            $responseBody = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('FaturaGsnetService - Resposta do recebimento de fatura recebida', [
+                'status_code' => $response->getStatusCode(),
+                'result_code' => $responseBody['ResultCode'] ?? null,
+                'message' => $responseBody['Message'] ?? null,
+                'data' => $responseBody['Data'] ?? null
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseBody['Message'] ?? 'Erro no recebimento da fatura', $responseBody);
+            }
+
+            $this->logRepository->storeReceberFaturasFarmanet(
+                $queryParams,
+                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse($responseBody, 'Fatura enviada para recebimento com sucesso');
+        } catch (CustomMessageException $e) {
+            $this->logRepository->storeReceberFaturasFarmanet(
+                $queryParams,
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
+            $this->logError('FaturaGsnetService@receberFaturas - Erro no recebimento da fatura', $e, [
+                'url' => $url ?? 'URL não definida',
+                'data' => $queryParams ?? []
+            ]);
+
+            $this->logRepository->storeReceberFaturasFarmanet(
+                $queryParams,
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (\Exception $e) {
+            $this->logError('FaturaGsnetService@receberFaturas - Erro interno no recebimento de faturas', $e);
+
+            $this->logRepository->storeReceberFaturasFarmanet(
+                $queryParams,
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse('Erro interno: ' . $e->getMessage())->setNullData();
+        }
+        return $response;
+    }
+
+    /**
+     * Buscar fatura por protocolo
+     *
+     * @param string $protocoloId
+     * @return FaturaGsnet|null
+     */
+    public function buscarPorProtocolo(string $protocoloId): ?FaturaGsnet
+    {
+        return FaturaGsnet::with(['itens', 'statusControle'])
+            ->where('protocolo_id_gsnet', $protocoloId)
+            ->where('ativo', true)
+            ->first();
+    }
+
+    /**
+     * Criar nova fatura
+     *
+     * @param array $dadosFatura
+     * @param array $itens
+     * @return FaturaGsnet
+     */
+    public function criarFatura(array $dadosFatura, array $itens = []): FaturaGsnet
+    {
+        try {
+            DB::connection('ministerio_saude_sp')->beginTransaction();
+
+            $fatura = FaturaGsnet::create($dadosFatura);
+
+            // Criar itens se fornecidos
+            foreach ($itens as $item) {
+                $item['fatura_id'] = $fatura->id;
+                $item['protocolo_id_gsnet'] = $fatura->protocolo_id_gsnet;
+                FaturaGsnetItem::create($item);
+            }
+
+            DB::connection('ministerio_saude_sp')->commit();
+
+            return $fatura->load(['itens', 'statusControle']);
+
+        } catch (\Exception $e) {
+            DB::connection('ministerio_saude_sp')->rollBack();
+            throw $e;
+        }
+    }
+
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/AtualizarStatusFaturaInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/AtualizarStatusFaturaInput.php
new file mode 100644
index 0000000..9dadb6e
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/AtualizarStatusFaturaInput.php
@@ -0,0 +1,55 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;
+
+class AtualizarStatusFaturaInput
+{
+    public string $protocoloIdGsnet;
+    public string $nrDocumento;
+    public int $idOrigem;
+    public ?string $justificativa;
+
+
+    public function __construct(
+        string $protocoloIdGsnet,
+        string $nrDocumento,
+        int $idOrigem,
+        ?string $justificativa
+    ) {
+        $this->protocoloIdGsnet = $protocoloIdGsnet;
+        $this->nrDocumento = $nrDocumento;
+        $this->idOrigem = $idOrigem;
+        $this->justificativa = $justificativa;
+    }
+
+    public static function fromArray(array $data): AtualizarStatusFaturaInput
+    {
+        return new self(
+            $data['protocolo_id_gsnet'],
+            $data['nr_documento'],
+            $data['id_origem'],
+            $data['justificativa'] ?? null,
+        );
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'protocolo_id_gsnet' => $this->protocoloIdGsnet,
+            'nr_documento' => $this->nrDocumento,
+            'id_origem' => $this->idOrigem,
+            'justificativa' => $this->justificativa
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        return [
+            'protocoloIdGsnet' => $this->protocoloIdGsnet,
+            'nrDocumento' => $this->nrDocumento,
+            'idOrigem' => $this->idOrigem,
+            'justificativa' => $this->justificativa,
+            'AccessToken' => $accessToken,
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ConsultaFaturaInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ConsultaFaturaInput.php
new file mode 100644
index 0000000..9a6347a
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ConsultaFaturaInput.php
@@ -0,0 +1,120 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;
+
+class ConsultaFaturaInput
+{
+    public string $idGestor;
+    public ?string $anoReferencia;
+    public ?string $mesReferencia;
+    public ?string $codigoPrograma;
+    public ?string $dataInicio;
+    public ?string $dataFim;
+    public ?string $localOrigemId;
+    public ?string $localDestinoId;
+    public ?string $status;
+    public ?string $page;
+    public ?string $perPage;
+
+
+    public function __construct(
+        string $idGestor,
+        ?string $anoReferencia,
+        ?string $mesReferencia,
+        ?string $codigoPrograma,
+        ?string $dataInicio,
+        ?string $dataFim,
+        ?string $localOrigemId,
+        ?string $localDestinoId,
+        ?string $status,
+        ?string $page,
+        ?string $perPage
+    ) {
+        $this->idGestor = $idGestor;
+        $this->anoReferencia  = $anoReferencia;
+        $this->mesReferencia  = $mesReferencia;
+        $this->codigoPrograma  = $codigoPrograma;
+        $this->dataInicio  = $dataInicio;
+        $this->dataFim  = $dataFim;
+        $this->localOrigemId  = $localOrigemId;
+        $this->localDestinoId  = $localDestinoId;
+        $this->status  = $status;
+        $this->page  = $page;
+        $this->perPage  = $perPage;
+    }
+
+    public static function fromArray(array $data): ConsultaFaturaInput
+    {
+        return new self(
+            $data['id_gestor'],
+            $data['ano_referencia'] ?? null,
+            $data['mes_referencia'] ?? null,
+            $data['codigo_programa'] ?? null,
+            $data['data_inicio'] ?? null,
+            $data['data_fim'] ?? null,
+            $data['local_origem_id'] ?? null,
+            $data['local_destino_id'] ?? null,
+            $data['status'] ?? null,
+            $data['page'] ?? null,
+            $data['per_page'] ?? null
+        );
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'id_gestor' => $this->idGestor,
+            'ano_referencia' => $this->anoReferencia,
+            'mes_referencia' => $this->mesReferencia,
+            'codigo_programa' => $this->codigoPrograma,
+            'data_inicio' => $this->dataInicio,
+            'data_fim' => $this->dataFim,
+            'local_origem_id' => $this->localOrigemId,
+            'local_destino_id' => $this->localDestinoId,
+            'status' => $this->status,
+            'page' => $this->page,
+            'per_page' => $this->perPage
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        $params = [
+            'IdGestor' => $this->idGestor,
+            'AccessToken' => $accessToken,
+        ];
+
+        if ($this->anoReferencia) {
+            $params['AnoReferencia'] = $this->anoReferencia;
+        }
+        if ($this->mesReferencia) {
+            $params['MesReferencia'] = $this->mesReferencia;
+        }
+        if ($this->codigoPrograma) {
+            $params['CodigoPrograma'] = $this->codigoPrograma;
+        }
+        if ($this->dataInicio) {
+            $params['DtEmissao'] = $this->dataInicio;
+        }
+        if ($this->dataFim) {
+            $params['DtEmissaoFim'] = $this->dataFim;
+        }
+        if ($this->localOrigemId) {
+            $params['LocalOrigemId'] = $this->localOrigemId;
+        }
+        if ($this->localDestinoId) {
+            $params['LocalDestinoId'] = $this->localDestinoId;
+        }
+        if ($this->status) {
+            $params['Status'] = $this->status;
+        }
+        if ($this->page) {
+            $params['Page'] = $this->page;
+        }
+        if ($this->perPage) {
+            $params['PerPage'] = $this->perPage;
+        }
+
+        return $params;
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ReceberFaturasInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ReceberFaturasInput.php
new file mode 100644
index 0000000..b73eb44
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/Fatura/Input/ReceberFaturasInput.php
@@ -0,0 +1,133 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;
+
+class ReceberFaturasInput
+{
+    public ?string $dtDocumento;
+    public ?string $nrDocumento;
+    public ?string $vlDocumento;
+    public ?string $idProgramaSaude;
+    public ?string $idLocalDestino;
+    public ?string $cdPedido;
+    /** @var ?ItemReceberInput[] */
+    public ?array $itens;
+    public ?string $systemCode;
+
+    public function __construct(
+        ?string $dtDocumento,
+        ?string $nrDocumento,
+        ?string $vlDocumento,
+        ?string $idProgramaSaude,
+        ?string $idLocalDestino,
+        ?string $cdPedido,
+        ?array $itens,
+        ?string $systemCode
+    ) {
+        $this->dtDocumento = $dtDocumento;
+        $this->nrDocumento = $nrDocumento;
+        $this->vlDocumento = $vlDocumento;
+        $this->idProgramaSaude = $idProgramaSaude;
+        $this->idLocalDestino = $idLocalDestino;
+        $this->cdPedido = $cdPedido;
+        $this->itens = $itens;
+        $this->systemCode = $systemCode;
+    }
+
+    public static function fromArray(array $data): ReceberFaturasInput
+    {
+        $itens = null;
+        if (isset($data['itens']) && is_array($data['itens'])) {
+            $itens = [];
+            foreach ($data['itens'] as $item) {
+                $itens[] = ItemReceberInput::fromArray($item);
+            }
+        }
+        return new self(
+            $data['dt_documento'] ?? null,
+            $data['nr_documento'] ?? null,
+            $data['vl_documento'] ?? null,
+            $data['id_programa_saude'] ?? null,
+            $data['id_local_destino'] ?? null,
+            $data['cd_pedido'] ?? null,
+            $itens,
+            $data['system_code'] ?? null
+        );
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'dt_documento' => $this->dtDocumento,
+            'nr_documento' => $this->nrDocumento,
+            'vl_documento' => $this->vlDocumento,
+            'id_programa_saude' => $this->idProgramaSaude,
+            'id_local_destino' => $this->idLocalDestino,
+            'cd_pedido' => $this->cdPedido,
+            'itens' => $this->itens,
+            'system_code' => $this->systemCode
+        ];
+    }
+
+    /**
+     * Converte para o formato JSON esperado pela API
+     *
+     * @return array
+     */
+    public function toQueryParams(string $accessToken): array
+    {
+        $itensArray = [];
+
+        foreach ($this->itens ?? [] as $item) {
+            $itensArray[] = $item->toQueryParams();
+        }
+
+        return [
+            'Data' => [
+                'DtDocumento' => $this->dtDocumento,
+                'NrDocumento' => $this->nrDocumento,
+                'VlDocumento' => $this->vlDocumento,
+                'IdProgramaSaude' => $this->idProgramaSaude,
+                'IdLocalDestino' => $this->idLocalDestino,
+                'cdPedido' => $this->cdPedido,
+                'Itens' => $itensArray
+            ],
+            'AccessToken' => $accessToken,
+            'SystemCode' => $this->systemCode
+        ];
+    }
+}
+
+
+class ItemReceberInput
+{
+    public ?string $codigoItem;
+    public ?string $quantidade;
+
+    public function __construct(?string $codigoItem, ?string $quantidade)
+    {
+        $this->codigoItem = $codigoItem;
+        $this->quantidade = $quantidade;
+    }
+
+    public static function fromArray(array $data): self
+    {
+        return new self($data['codigo_item'] ?? null, $data['quantidade'] ?? null);
+    }
+
+    public function toQueryParams(): array
+    {
+        return [
+            'CodigoItem' => $this->codigoItem,
+            'Quantidade' => $this->quantidade
+        ];
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'codigo_item' => $this->codigoItem,
+            'quantidade' => $this->quantidade
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/ConsultarStatusInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/ConsultarStatusInput.php
new file mode 100644
index 0000000..f31067b
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/ConsultarStatusInput.php
@@ -0,0 +1,67 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;
+
+use Illuminate\Http\Request;
+
+class ConsultarStatusInput
+{
+    public ?string $idOrigem;
+    public ?string $nomeStatus;
+    public ?string $idStatus;
+
+    public function __construct(?string $idOrigem, ?string $idStatus, ?string $nomeStatus)
+    {
+        $this->idOrigem = $idOrigem;
+        $this->idStatus  = $idStatus;
+        $this->nomeStatus = $nomeStatus;
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'id_origem' => $this->idOrigem,
+            'id_status' => $this->idStatus,
+            'nome_status' => $this->nomeStatus,
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        $params = [
+            'AccessToken' => $accessToken
+        ];
+
+        if ($this->idOrigem) {
+            $params['IdOrigem'] = $this->idOrigem;
+        }
+
+        if ($this->idStatus) {
+            $params['IdStatus'] = $this->idStatus;
+        }
+
+        if ($this->nomeStatus) {
+            $params['NomeStatus'] = $this->nomeStatus;
+        }
+
+        return $params;
+    }
+
+    public static function fromRequest(Request $request): ConsultarStatusInput
+    {
+        return new self(
+            $request->input('id_origem') ?? null,
+            $request->input('id_status') ?? null,
+            $request->input('nome_status') ?? null,
+        );
+    }
+
+    public static function fromArray(array $data): ConsultarStatusInput
+    {
+        return new self(
+            $data['id_origem'] ?? null,
+            $data['id_status'] ?? null,
+            $data['nome_status'] ?? null,
+        );
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/CriarStatusInput.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/CriarStatusInput.php
new file mode 100644
index 0000000..46093ea
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/Input/CriarStatusInput.php
@@ -0,0 +1,64 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;
+
+use Illuminate\Http\Request;
+
+class CriarStatusInput
+{
+    public string $idOrigem;
+    public string $nomeStatus;
+    public string $descricaoStatus;
+    public ?string $systemCode = null;
+
+    public function __construct(string $idOrigem, string $nomeStatus, string $descricaoStatus, ?string $systemCode = null)
+    {
+        $this->idOrigem = $idOrigem;
+        $this->nomeStatus = $nomeStatus;
+        $this->descricaoStatus = $descricaoStatus;
+        $this->systemCode = $systemCode;
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'id_origem' => $this->idOrigem,
+            'nome_status' => $this->nomeStatus,
+            'descricao_status' => $this->descricaoStatus,
+            'system_code' => $this->systemCode
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        return [
+            'Data' => [
+                'IdOrigem' => $this->idOrigem,
+                'NomeStatus' => $this->nomeStatus,
+                'DescricaoStatus' => $this->descricaoStatus
+            ],
+            'AccessToken' => $accessToken,
+            'SystemCode' => $this->systemCode ?? config('ministerio_saude.api.system_code', 'IBL_LOGISTICS')
+        ];
+    }
+
+    public static function fromRequest(Request $request): CriarStatusInput
+    {
+        return new self(
+            $request->input('id_origem'),
+            $request->input('nome_status'),
+            $request->input('descricao_status'),
+            $request->input('system_code')
+        );
+    }
+
+    public static function fromArray(array $data): CriarStatusInput
+    {
+        return new self(
+            $data['id_origem'],
+            $data['nome_status'],
+            $data['descricao_status'],
+            $data['system_code'] ?? null
+        );
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/StatusOperadorService.php b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/StatusOperadorService.php
new file mode 100644
index 0000000..dce4b78
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/FaturaFarmanet/StatusOperador/StatusOperadorService.php
@@ -0,0 +1,253 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador;
+
+use Domain\MinisterioSaude\Exceptions\CustomMessageException;
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Domain\MinisterioSaude\Traits\HasServiceResponse;
+use Exception;
+use Illuminate\Support\Facades\Log;
+use Illuminate\Support\Facades\DB;
+use GuzzleHttp\Client;
+use GuzzleHttp\Exception\RequestException;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\CriarStatusInput;
+use Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input\ConsultarStatusInput;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\StatusOperadorRepositoryInterface;
+use Domain\MinisterioSaude\Exceptions\DuplicateRecordException;
+
+class StatusOperadorService
+{
+    use HasServiceResponse;
+    use HasLog;
+
+    private $client;
+    private $baseUrl;
+    private $accessToken;
+    private StatusOperadorRepositoryInterface $repository;
+    private MinisterioSaudeLogRepositoryInterface $logRepository;
+    public function __construct(StatusOperadorRepositoryInterface $repository, MinisterioSaudeLogRepositoryInterface $logRepository)
+    {
+        $this->client = new Client([
+            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
+            'verify' => false,
+        ]);
+
+        $environment = config('ministerio_saude.farmanet.environment', 'homolog');
+        $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
+        $this->repository = $repository;
+        $this->accessToken = config('ministerio_saude.api.access_token');
+        $this->logRepository = $logRepository;
+    }
+
+    /**
+     * API 1.1 - Inserir Status na Fatura
+     * Criar novo status tanto localmente quanto na API
+     */
+    public function criarStatus(CriarStatusInput $input): ServiceResponse
+    {
+        try {
+            DB::connection('ministerio_saude_sp')->beginTransaction();
+
+            $statusExistente = $this->repository->getOneWhere(
+                fn ($query) =>
+                $query->where('id_origem', $input->idOrigem)
+                    ->orWhere('nome_status', $input->nomeStatus)
+            );
+
+            if ($statusExistente) {
+                DB::connection('ministerio_saude_sp')->rollBack();
+
+                $duplicatedFields = $this->getDuplicatedFields($statusExistente, $input);
+                $message = $this->buildDuplicationMessage($duplicatedFields);
+
+                throw new CustomMessageException($message, null, 'VALIDATION_ERROR', 422);
+            }
+
+            $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.inserir_status_op');
+
+            $requestData = $input->toQueryParams($this->accessToken);
+
+            $this->logInfo('StatusOperadorService - Chamando API real do Ministério', [
+                'url' => $url,
+                'data' => collect($requestData)->except(['AccessToken'])->toArray()
+            ]);
+
+            $response = $this->client->post($url, ['json' => $requestData]);
+            $responseBody = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('StatusOperadorService - Resposta da API real recebida', [
+                'status_code' => $response->getStatusCode(),
+                'response' => $responseBody
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na inclusão', $responseBody);
+            }
+
+            $status = $this->repository->store([
+                'id_origem' => $input->idOrigem,
+                'nome_status' => $input->nomeStatus,
+                'descricao_status' => $input->descricaoStatus,
+                'flag_registro' => true
+            ]);
+
+            DB::connection('ministerio_saude_sp')->commit();
+
+            $this->logInfo('StatusOperadorService - Status criado com sucesso', [
+                'status_id' => $status->get('id'),
+                'id_origem' => $input->idOrigem,
+                'nome_status' => $input->nomeStatus,
+                'api_externa_usada' => true
+            ]);
+            $this->logRepository->storeInserirStatusFatura(
+                $requestData,
+                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse($status, 'Status incluído com sucesso');
+        } catch (CustomMessageException $e) {
+            DB::connection('ministerio_saude_sp')->rollBack();
+
+            $this->logError('StatusOperadorService - Erro ao criar status', $e);
+            $this->logRepository->storeInserirStatusFatura(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            DB::connection('ministerio_saude_sp')->rollBack();
+
+            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
+            $this->logError('StatusOperadorService - Erro na API externa', $e, [
+                'url' => $url ?? 'URL não definida',
+                'data' => $input->toArray() ?? []
+            ]);
+
+            $this->logRepository->storeInserirStatusFatura(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (Exception $e) {
+            DB::connection('ministerio_saude_sp')->rollBack();
+            $this->logError('StatusOperadorService - Erro ao criar status', $e);
+            $response = $this->failureResponse("Erro interno ao criar status: {$e->getMessage()}")->setNullData();
+        }
+        return $response;
+    }
+
+    /**
+     * API 1.2 - Consultar Status do Operador
+     * Consultar status via API real do Ministério
+     */
+    public function consultarStatus(ConsultarStatusInput $input): ServiceResponse
+    {
+        try {
+
+            $url = $this->baseUrl . config('ministerio_saude.farmanet.endpoints.consultar_status_op');
+
+            $queryParams = $input->toQueryParams($this->accessToken);
+            $this->logInfo('StatusOperadorService - Consultando status via API real', [
+                'url' => $url,
+                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
+            ]);
+
+            $response = $this->client->get($url, [
+                'query' => $queryParams,
+                'headers' => [
+                    'Accept' => 'application/json',
+                ]
+            ]);
+            $responseBody = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('StatusOperadorService - Resposta da API real recebida', [
+                'status_code' => $response->getStatusCode(),
+                'total_records' => count($responseBody['Data'] ?? [])
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na consulta', $responseBody);
+            }
+
+            $this->logRepository->storeConsultarStatus(
+                $input->toArray(),
+                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse($responseBody, 'Consulta realizada com sucesso');
+        } catch (CustomMessageException $e) {
+            $this->logError('StatusOperadorService - Erro na consulta da API externa', $e, [
+                'url' => $url ?? 'URL não definida',
+                'params' => $input->toArray()
+            ])->setData($e->getData());
+            $this->logRepository->storeConsultarStatus(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
+            $this->logError('StatusOperadorService - Erro na consulta da API externa', $e, ['url' => $url ?? 'URL não definida']);
+            $this->logRepository->storeConsultarStatus(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (Exception $e) {
+            $this->logError('StatusOperadorService - Erro ao consultar status', $e);
+            $this->logRepository->storeConsultarStatus(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse("Erro interno ao consultar status: {$e->getMessage()}")->setNullData();
+        }
+        return $response;
+    }
+
+    private function getDuplicatedFields($statusExistente, $data): array
+    {
+        $duplicatedFields = [];
+
+        $fieldMappings = [
+            'id_origem' => ['field' => 'idOrigem', 'label' => 'Id de Origem'],
+            'nome_status' => ['field' => 'nomeStatus', 'label' => 'Nome do Status']
+        ];
+
+        foreach ($fieldMappings as $dbField => $mapping) {
+            if ($statusExistente->{$dbField} == $data->{$mapping['field']}) {
+                $duplicatedFields[] = $mapping['label'];
+            }
+        }
+
+        return $duplicatedFields;
+    }
+
+    private function buildDuplicationMessage(array $duplicatedFields): string
+    {
+        if (empty($duplicatedFields)) {
+            return 'Registro já existe na base';
+        }
+
+        if (count($duplicatedFields) === 1) {
+            return "{$duplicatedFields[0]} já consta na base";
+        }
+
+        return implode(' e ', $duplicatedFields) . ' já constam na base';
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/FaturaGsnetService.php b/app/Domain/MinisterioSaude/Services/FaturaGsnetService.php
deleted file mode 100644
index c4f93fb..0000000
--- a/app/Domain/MinisterioSaude/Services/FaturaGsnetService.php
+++ /dev/null
@@ -1,336 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Services;
-
-use App\Models\FaturaGsnet;
-use App\Models\FaturaGsnetItem;
-use App\Models\FaturaGsnetStatusControle;
-use Illuminate\Support\Facades\DB;
-use Illuminate\Support\Facades\Log;
-use Carbon\Carbon;
-use GuzzleHttp\Client;
-use GuzzleHttp\Exception\RequestException;
-
-class FaturaGsnetService
-{
-    private Client $client;
-    private string $baseUrl;
-    
-    public function __construct()
-    {
-        $this->client = new Client([
-            'timeout' => config('ministerio_saude.api.timeout', 30),
-            'verify' => false,
-        ]);
-        
-        $environment = config('ministerio_saude.api.environment', 'homolog');
-        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
-    }
-    /**
-     * API 2.1 - Consultar Faturas via API real do Ministério
-     * 
-     * @param array $data
-     * @return array
-     */
-    public function consultarFaturas(array $data): array
-    {
-        try {
-            $endpoint = config('ministerio_saude.endpoints.consultar_faturas_gsnet');
-            $accessToken = config('ministerio_saude.api.access_token');
-            
-            // Montar query parameters baseado no seu exemplo
-            $queryParams = [
-                'AccessToken' => $accessToken,
-                'IdGestor' => $data['id_gestor']
-            ];
-            
-            // Adicionar datas de emissão se fornecidas
-            if (isset($data['data_inicio'])) {
-                $queryParams['DtEmissao'] = $data['data_inicio'];
-            }
-            if (isset($data['data_fim'])) {
-                $queryParams['DtEmissaoFim'] = $data['data_fim'];
-            }
-            
-            // Adicionar outros parâmetros opcionais
-            if (isset($data['ano_referencia'])) {
-                $queryParams['AnoReferencia'] = $data['ano_referencia'];
-            }
-            if (isset($data['mes_referencia'])) {
-                $queryParams['MesReferencia'] = $data['mes_referencia'];
-            }
-            if (isset($data['codigo_programa'])) {
-                $queryParams['CodigoPrograma'] = $data['codigo_programa'];
-            }
-            if (isset($data['status'])) {
-                $queryParams['Status'] = $data['status'];
-            }
-            
-            $url = $this->baseUrl . $endpoint . '?' . http_build_query($queryParams);
-            
-            Log::info('FaturaGsnetService - Consultando faturas via API real', [
-                'url' => $url,
-                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
-            ]);
-            
-            $response = $this->client->get($url, [
-                'headers' => [
-                    'Accept' => 'application/json',
-                ]
-            ]);
-            
-            $responseBody = json_decode($response->getBody()->getContents(), true);
-            
-            Log::info('FaturaGsnetService - Resposta da API real recebida', [
-                'status_code' => $response->getStatusCode(),
-                'result_code' => $responseBody['ResultCode'] ?? null,
-                'message' => $responseBody['Message'] ?? null,
-                'total_faturas' => count($responseBody['Data'] ?? [])
-            ]);
-            
-            return [
-                'success' => true,
-                'data' => $responseBody,
-                'message' => 'Consulta de faturas realizada com sucesso via API real do Ministério.',
-                'total_faturas' => count($responseBody['Data'] ?? []),
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-
-        } catch (RequestException $e) {
-            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
-            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
-            
-            Log::error('FaturaGsnetService - Erro na requisição para o Ministério', [
-                'message' => $e->getMessage(),
-                'status_code' => $statusCode,
-                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
-                'request_data' => $data
-            ]);
-
-            return [
-                'success' => false,
-                'message' => $errorMessage,
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-
-        } catch (\Exception $e) {
-            Log::error('FaturaGsnetService - Erro geral', [
-                'message' => $e->getMessage(),
-                'trace' => $e->getTraceAsString(),
-                'request_data' => $data
-            ]);
-
-            return [
-                'success' => false,
-                'message' => 'Erro ao consultar faturas: ' . $e->getMessage(),
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-        }
-    }
-    
-    /**
-            ]);
-            
-            return [
-                'success' => false,
-                'message' => $errorMessage
-            ];
-
-            $faturas = $query->orderBy('data_criacao', 'desc')
-                ->paginate($perPage, ['*'], 'page', $page);
-
-            // Formatação para a resposta da API
-            $response = [
-                'Data' => [],
-                'Meta' => [
-                    'TotalItens' => $faturas->total(),
-                    'PaginaAtual' => $faturas->currentPage(),
-                    'ItensPorPagina' => $faturas->perPage(),
-                    'TotalPaginas' => $faturas->lastPage()
-                ]
-            ];
-
-            foreach ($faturas->items() as $fatura) {
-                $faturaData = [
-                    'ProtocoloIdGsnet' => $fatura->protocolo_id_gsnet,
-                    'IdGestor' => $fatura->id_gestor,
-                    'NrDocumento' => $fatura->nr_documento,
-                    'DescricaoDocumento' => $fatura->descricao_documento,
-                    'NrProcesso' => $fatura->nr_processo,
-                    'DescricaoProcesso' => $fatura->descricao_processo,
-                    'ValorTotal' => (float) $fatura->valor_total,
-                    'LocalOrigem' => [
-                        'Id' => $fatura->local_origem_id,
-                        'Codigo' => $fatura->local_origem_codigo,
-                        'Descricao' => $fatura->local_origem_descricao
-                    ],
-                    'LocalDestino' => [
-                        'Id' => $fatura->local_destino_id,
-                        'Codigo' => $fatura->local_destino_codigo,
-                        'Descricao' => $fatura->local_destino_descricao
-                    ],
-                    'StatusAtual' => $fatura->status_atual,
-                    'DataCriacao' => $fatura->data_criacao->format('Y-m-d H:i:s'),
-                    'DataUltimaAtualizacao' => $fatura->data_ultima_atualizacao->format('Y-m-d H:i:s'),
-                    'Itens' => []
-                ];
-
-                // Itens da fatura
-                foreach ($fatura->itens as $item) {
-                    $faturaData['Itens'][] = [
-                        'CodigoMaterial' => (int) $item->codigo_material,
-                        'CodigoSiafisico' => (int) $item->codigo_siafisico,
-                        'NomeMaterial' => $item->nome_material,
-                        'QuantidadeMaterial' => (float) $item->quantidade_material,
-                        'PrecoMedio' => (float) $item->preco_medio
-                    ];
-                }
-
-                $response['Data'][] = $faturaData;
-            }
-
-            return $response;
-
-        } catch (\Exception $e) {
-            Log::error('FaturaGsnetService@consultarFaturas - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            throw $e;
-        }
-    }
-
-    /**
-     * API 2.2 - Atualizar Status da Fatura
-     * 
-     * @param array $data
-     * @return array
-     */
-    public function atualizarStatusFatura(array $data): array
-    {
-        try {
-            DB::connection('ministerio_saude_sp')->beginTransaction();
-
-            // Detectar formato e extrair dados
-            if (isset($data['Data'])) {
-                // Formato original
-                $protocoloId = $data['Data']['ProtocoloIdGsnet'];
-                $statusCodigo = $data['Data']['StatusCodigo'];
-                $statusDescricao = $data['Data']['StatusDescricao'];
-                $observacao = $data['Data']['Observacao'] ?? null;
-                $dataStatus = $data['Data']['DataStatus'];
-            } else {
-                // Formato novo - usar numero_fatura como protocolo
-                $protocoloId = $data['numero_fatura'];
-                $statusCodigo = (string) $data['status_fatura'];
-                $statusDescricao = "Status {$data['status_fatura']} - Atualizado via API";
-                $observacao = $data['observacoes'] ?? null;
-                $dataStatus = $data['data_atualizacao'];
-            }
-            
-            // Buscar a fatura
-            $fatura = FaturaGsnet::where('protocolo_id_gsnet', $protocoloId)
-                ->where('ativo', true)
-                ->first();
-
-            if (!$fatura) {
-                // Se não encontrar, criar uma nova fatura para teste (modo desenvolvimento)
-                $fatura = FaturaGsnet::create([
-                    'protocolo_id_gsnet' => $protocoloId,
-                    'nr_documento' => $protocoloId, // Usar o protocolo como número do documento
-                    'id_gestor' => $data['id_gestor'] ?? '0',
-                    'status_atual' => $statusCodigo,
-                    'data_criacao' => now(),
-                    'data_ultima_atualizacao' => Carbon::parse($dataStatus),
-                    'ativo' => true
-                ]);
-                
-                $mensagemBase = 'Nova fatura criada e status definido com sucesso';
-            } else {
-                // Atualizar status atual da fatura existente
-                $fatura->status_atual = $statusCodigo;
-                $fatura->data_ultima_atualizacao = Carbon::parse($dataStatus);
-                $fatura->save();
-                
-                $mensagemBase = 'Status da fatura atualizado com sucesso';
-            }
-
-            // Registrar no controle de status
-            FaturaGsnetStatusControle::create([
-                'fatura_id' => $fatura->id,
-                'protocolo_id_gsnet' => $protocoloId,
-                'status_codigo' => $statusCodigo,
-                'status_descricao' => $statusDescricao,
-                'observacao' => $observacao,
-                'data_status' => Carbon::parse($dataStatus),
-                'enviado_ministerio' => false
-            ]);
-
-            DB::connection('ministerio_saude_sp')->commit();
-
-            return [
-                'success' => true,
-                'message' => $mensagemBase . ' (modo desenvolvimento)'
-            ];
-
-        } catch (\Exception $e) {
-            DB::connection('ministerio_saude_sp')->rollBack();
-            
-            Log::error('FaturaGsnetService@atualizarStatusFatura - Erro', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return [
-                'success' => false,
-                'message' => 'Erro ao atualizar status da fatura: ' . $e->getMessage()
-            ];
-        }
-    }
-
-    /**
-     * Buscar fatura por protocolo
-     * 
-     * @param string $protocoloId
-     * @return FaturaGsnet|null
-     */
-    public function buscarPorProtocolo(string $protocoloId): ?FaturaGsnet
-    {
-        return FaturaGsnet::with(['itens', 'statusControle'])
-            ->where('protocolo_id_gsnet', $protocoloId)
-            ->where('ativo', true)
-            ->first();
-    }
-
-    /**
-     * Criar nova fatura
-     * 
-     * @param array $dadosFatura
-     * @param array $itens
-     * @return FaturaGsnet
-     */
-    public function criarFatura(array $dadosFatura, array $itens = []): FaturaGsnet
-    {
-        try {
-            DB::connection('ministerio_saude_sp')->beginTransaction();
-
-            $fatura = FaturaGsnet::create($dadosFatura);
-
-            // Criar itens se fornecidos
-            foreach ($itens as $item) {
-                $item['fatura_id'] = $fatura->id;
-                $item['protocolo_id_gsnet'] = $fatura->protocolo_id_gsnet;
-                FaturaGsnetItem::create($item);
-            }
-
-            DB::connection('ministerio_saude_sp')->commit();
-
-            return $fatura->load(['itens', 'statusControle']);
-
-        } catch (\Exception $e) {
-            DB::connection('ministerio_saude_sp')->rollBack();
-            throw $e;
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Services/Item/Input/ConsultarItensInput.php b/app/Domain/MinisterioSaude/Services/Item/Input/ConsultarItensInput.php
new file mode 100644
index 0000000..7bf6cb8
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Item/Input/ConsultarItensInput.php
@@ -0,0 +1,81 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Item\Input;
+
+class ConsultarItensInput
+{
+    /** @var ItemMaterial[] */
+    public $listaItens;
+
+    /**
+     * @param ItemMaterial[] $listaItens
+     */
+    public function __construct(array $listaItens)
+    {
+        $this->listaItens = $listaItens;
+    }
+
+    public static function fromArray(array $data): ConsultarItensInput
+    {
+        $listaItens = [];
+
+        if (isset($data['lista_itens']) && is_array($data['lista_itens'])) {
+            foreach ($data['lista_itens'] as $item) {
+                if (isset($item['codigo_material'])) {
+                    $listaItens[] = ItemMaterial::fromArray($item);
+                }
+            }
+        }
+
+        return new self($listaItens);
+    }
+
+    public function toArray(): array
+    {
+        $listaItensArray = [];
+        foreach ($this->listaItens as $item) {
+            $listaItensArray[] = $item->toArray();
+        }
+
+        return [
+            'lista_itens' => $listaItensArray
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        $listaItensArray = [];
+        foreach ($this->listaItens as $item) {
+            $listaItensArray[] = $item->toArray();
+        }
+
+        return [
+            'Data' => [
+                'ListaItens' => $listaItensArray
+            ],
+            'AccessToken' => $accessToken
+        ];
+    }
+}
+
+class ItemMaterial
+{
+    public int $codigoMaterial;
+
+    public function __construct(int $codigoMaterial)
+    {
+        $this->codigoMaterial = $codigoMaterial;
+    }
+
+    public static function fromArray(array $data): self
+    {
+        return new self($data['codigo_material']);
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'CodigoMaterial' => $this->codigoMaterial
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/Item/Input/ObterPrecoMedioItensInput.php b/app/Domain/MinisterioSaude/Services/Item/Input/ObterPrecoMedioItensInput.php
new file mode 100644
index 0000000..621e3f0
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Item/Input/ObterPrecoMedioItensInput.php
@@ -0,0 +1,81 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Item\Input;
+
+class ObterPrecoMedioItensInput
+{
+    /** @var ObterPrecoMedioListaItensInput[] */
+    public $listaItens;
+
+    /**
+     * @param ObterPrecoMedioListaItensInput[] $listaItens
+     */
+    public function __construct(array $listaItens)
+    {
+        $this->listaItens = $listaItens;
+    }
+
+    public static function fromArray(array $data): ObterPrecoMedioItensInput
+    {
+        $listaItens = [];
+
+        if (isset($data['lista_itens']) && is_array($data['lista_itens'])) {
+            foreach ($data['lista_itens'] as $item) {
+                if (isset($item['codigo_item'])) {
+                    $listaItens[] = ObterPrecoMedioListaItensInput::fromArray($item);
+                }
+            }
+        }
+
+        return new self($listaItens);
+    }
+
+    public function toArray(): array
+    {
+        $listaItensArray = [];
+        foreach ($this->listaItens as $item) {
+            $listaItensArray[] = $item->toArray();
+        }
+
+        return [
+            'lista_itens' => $listaItensArray
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        $listaItensArray = [];
+        foreach ($this->listaItens as $item) {
+            $listaItensArray[] = $item->toArray();
+        }
+
+        return [
+            'Data' => [
+                'ListaItens' => $listaItensArray
+            ],
+            'AccessToken' => $accessToken
+        ];
+    }
+}
+
+class ObterPrecoMedioListaItensInput
+{
+    public ?int $codigoItem;
+
+    public function __construct(int $codigoItem)
+    {
+        $this->codigoItem = $codigoItem;
+    }
+
+    public static function fromArray(array $data): self
+    {
+        return new self($data['codigo_item'] ?? null);
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'CodigoItem' => $this->codigoItem
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/Item/ItemService.php b/app/Domain/MinisterioSaude/Services/Item/ItemService.php
new file mode 100644
index 0000000..e2a0f00
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Item/ItemService.php
@@ -0,0 +1,215 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Item;
+
+use Domain\MinisterioSaude\Exceptions\CustomMessageException;
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Services\Item\Input\ConsultarItensInput;
+use Domain\MinisterioSaude\Services\Item\Input\ObterPrecoMedioItensInput;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Domain\MinisterioSaude\Traits\HasServiceResponse;
+use GuzzleHttp\Client;
+use GuzzleHttp\Exception\RequestException;
+use Carbon\Carbon;
+
+class ItemService
+{
+    use HasServiceResponse;
+    use HasLog;
+
+    private $client;
+    private $baseUrl;
+    private $accessToken;
+    private MinisterioSaudeLogRepositoryInterface $logRepository;
+
+    public function __construct(
+        MinisterioSaudeLogRepositoryInterface $logRepository
+    ) {
+        $this->client = new Client([
+            'timeout' => config('ministerio_saude.items.timeout', 30),
+            'verify' => false,
+        ]);
+
+        $environment = config('ministerio_saude.items.environment', 'homolog');
+        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
+        $this->accessToken = config('ministerio_saude.api.access_token');
+        $this->logRepository = $logRepository;
+    }
+
+    /**
+     * API 1.4 - Consultar Itens do Sistema GSNET
+     *
+     * Consulta itens no sistema GSNET do Ministério da Saúde
+     *
+     * @param ConsultarItensInput $input
+     * @return ServiceResponse
+     */
+    public function consultarItens(ConsultarItensInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.items.endpoints.consultar_itens');
+            $url = "{$this->baseUrl}{$endpoint}";
+
+            $this->logInfo('ConsultarItensService - Iniciando consulta de itens via API real', [
+                'url' => $url,
+                'total_itens' => count($input->listaItens),
+                'codigos_materiais' => array_column($input->listaItens, 'CodigoMaterial')
+            ]);
+
+            $payload = $input->toQueryParams($this->accessToken);
+            $response = $this->client->put($url, ['json' => $payload]);
+            $responseData = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('ConsultarItensService - Resposta recebida do Ministério', [
+                'status_code' => $response->getStatusCode(),
+                'total_itens_retornados' => count($responseData['Data'] ?? []),
+                'protocolo' => $responseData['Protocolo'] ?? null
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
+            }
+
+            $this->logRepository->storeConsultarItens(
+                $input->toArray(),
+                array_merge($responseData, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse([
+                'result' => $responseData,
+                'total_itens' => count($responseData['Data'] ?? []),
+                'timestamp' => Carbon::now()->toISOString()
+            ], 'Consulta de itens realizada com sucesso via API real do Ministério.');
+        } catch (CustomMessageException $e) {
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+            $this->logRepository->storeConsultarItens(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
+
+            $this->logError('ConsultarItensService - Erro na requisição para o Ministério', $e, [
+                'status_code' => $statusCode,
+                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
+                'request_data' => $input
+            ]);
+            $this->logRepository->storeConsultarItens(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+        } catch (\Exception $e) {
+            $this->logError('ConsultarItensService - Erro geral', $e, ['request_data' => $input]);
+            $this->logRepository->storeConsultarItens(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse("Erro ao consultar itens: {$e->getMessage()}")
+                ->setNullData()
+                ->setErrorCode('INTERNAL_ERROR');
+        }
+        return $response;
+    }
+
+    /**
+     * API 3.4 - Obter Preço Médio de Itens
+     *
+     * @param ObterPrecoMedioItensInput $input
+     * @return ServiceResponse
+     */
+    public function obterPrecoMedio(ObterPrecoMedioItensInput $input): ServiceResponse
+    {
+
+        try {
+            $endpoint = config('ministerio_saude.items.endpoints.obter_preco_medio');
+            $url = "{$this->baseUrl}{$endpoint}";
+
+            $this->logInfo('ConsultarItensService - Iniciando consulta de itens via API real', [
+                'url' => $url,
+                'total_itens' => count($input->listaItens),
+                'codigos_materiais' => array_column($input->listaItens, 'CodigoMaterial')
+            ]);
+
+            $payload = $input->toQueryParams($this->accessToken);
+            $response = $this->client->post($url, ['json' => $payload]);
+            $responseData = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('ConsultarItensService - Resposta recebida do Ministério', [
+                'status_code' => $response->getStatusCode(),
+                'total_itens_retornados' => count($responseData['Data'] ?? []),
+                'protocolo' => $responseData['Protocolo'] ?? null
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
+            }
+
+            $this->logRepository->storeObterPrecoMedioItens(
+                $input->toArray(),
+                array_merge($responseData, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse([
+                'result' => $responseData,
+                'total_itens' => count($responseData['Data'] ?? []),
+                'timestamp' => Carbon::now()->toISOString()
+            ], 'Consulta de itens realizada com sucesso via API real do Ministério.');
+        } catch (CustomMessageException $e) {
+            $this->logError('ConsultarItensService - Erro na consulta da API externa', $e, [
+                'url' => $url ?? 'URL não definida',
+                'params' => $input->toArray() ?? []
+            ])->setData($e->getData());
+            $this->logRepository->storeObterPrecoMedioItens(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
+
+            $this->logError('ConsultarItensService - Erro na requisição para o Ministério', $e, [
+                'status_code' => $statusCode,
+                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
+                'request_data' => $input
+            ]);
+
+            $this->logRepository->storeObterPrecoMedioItens(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+
+        } catch (\Exception $e) {
+            $this->logError('ConsultarItensService - Erro geral', $e, ['request_data' => $input]);
+            $this->logRepository->storeObterPrecoMedioItens(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse("Erro ao consultar itens: {$e->getMessage()}")
+                ->setNullData()
+                ->setErrorCode('INTERNAL_ERROR');
+        }
+        return $response;
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/Planejamento/Input/AtualizarPedidoInput.php b/app/Domain/MinisterioSaude/Services/Planejamento/Input/AtualizarPedidoInput.php
new file mode 100644
index 0000000..3884699
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Planejamento/Input/AtualizarPedidoInput.php
@@ -0,0 +1,45 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Planejamento\Input;
+
+class AtualizarPedidoInput
+{
+    public int $idProgramaSaude;
+    public int $codigoPedido;
+    public string $protocoloOperador;
+
+    public function __construct(int $idProgramaSaude, int $codigoPedido, string $protocoloOperador)
+    {
+        $this->idProgramaSaude = $idProgramaSaude;
+        $this->codigoPedido = $codigoPedido;
+        $this->protocoloOperador = $protocoloOperador;
+    }
+
+    public static function fromArray(array $data): AtualizarPedidoInput
+    {
+        return new self(
+            $data['id_programa_saude'],
+            $data['codigo_pedido'],
+            $data['protocolo_operador']
+        );
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'id_programa_saude' => $this->idProgramaSaude,
+            'codigo_pedido' => $this->codigoPedido,
+            'protocolo_operador' => $this->protocoloOperador,
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        return [
+            'IdProgramaSaude' => $this->idProgramaSaude,
+            'CodigoPedido' => $this->codigoPedido,
+            'ProtocoloOperador' => $this->protocoloOperador,
+            'AccessToken' => $accessToken
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/Planejamento/Input/ConsultarPedidosInput.php b/app/Domain/MinisterioSaude/Services/Planejamento/Input/ConsultarPedidosInput.php
new file mode 100644
index 0000000..3c51249
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Planejamento/Input/ConsultarPedidosInput.php
@@ -0,0 +1,55 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Planejamento\Input;
+
+class ConsultarPedidosInput
+{
+    public ?int $codigoPrograma;
+    public ?int $anoReferencia;
+    public ?int $mesReferencia;
+    public ?int $anoPeriodoReferencia;
+    public ?int $idGestor;
+
+    public function __construct(?int $codigoPrograma, ?int $anoReferencia, ?int $mesReferencia, ?int $anoPeriodoReferencia, ?int $idGestor)
+    {
+        $this->codigoPrograma = $codigoPrograma;
+        $this->anoReferencia = $anoReferencia;
+        $this->mesReferencia = $mesReferencia;
+        $this->anoPeriodoReferencia = $anoPeriodoReferencia;
+        $this->idGestor = $idGestor;
+    }
+
+    public static function fromArray(array $data): ConsultarPedidosInput
+    {
+        return new self(
+            $data['codigo_programa'] ?? null,
+            $data['ano_referencia'] ?? null,
+            $data['mes_referencia'] ?? null,
+            $data['ano_periodo_referencia'] ?? null,
+            $data['id_gestor'] ?? null
+        );
+    }
+
+    public function toArray(): array
+    {
+        return [
+            'codigo_programa' => $this->codigoPrograma,
+            'ano_referencia' => $this->anoReferencia,
+            'mes_referencia' => $this->mesReferencia,
+            'ano_periodo_referencia' => $this->anoPeriodoReferencia,
+            'id_gestor' => $this->idGestor
+        ];
+    }
+
+    public function toQueryParams(string $accessToken): array
+    {
+        return [
+            'CodigoPrograma' => $this->codigoPrograma,
+            'AnoReferencia' => $this->anoReferencia,
+            'MesReferencia' => $this->mesReferencia,
+            'AnoPeriodoReferencia' => $this->anoPeriodoReferencia,
+            'IdGestor' => $this->idGestor,
+            'AccessToken' => $accessToken
+        ];
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/Planejamento/PlanejamentoService.php b/app/Domain/MinisterioSaude/Services/Planejamento/PlanejamentoService.php
new file mode 100644
index 0000000..f35d4b0
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Services/Planejamento/PlanejamentoService.php
@@ -0,0 +1,225 @@
+<?php
+
+namespace Domain\MinisterioSaude\Services\Planejamento;
+
+use Domain\MinisterioSaude\Exceptions\CustomMessageException;
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
+use Domain\MinisterioSaude\Services\Planejamento\Input\AtualizarPedidoInput;
+use Domain\MinisterioSaude\Services\Planejamento\Input\ConsultarPedidosInput;
+use Domain\MinisterioSaude\Traits\HasLog;
+use Domain\MinisterioSaude\Traits\HasServiceResponse;
+use GuzzleHttp\Client;
+use GuzzleHttp\Exception\RequestException;
+use Carbon\Carbon;
+
+class PlanejamentoService
+{
+    use HasServiceResponse;
+    use HasLog;
+
+    private $client;
+    private $baseUrl;
+    private $accessToken;
+    private MinisterioSaudeLogRepositoryInterface $logRepository;
+
+    public function __construct(
+        MinisterioSaudeLogRepositoryInterface $logRepository
+    ) {
+        $this->client = new Client([
+            'timeout' => config('ministerio_saude.items.timeout', 30),
+            'verify' => false,
+        ]);
+
+        $environment = config('ministerio_saude.items.environment', 'homolog');
+        $this->baseUrl = config("ministerio_saude.items.base_url.{$environment}");
+        $this->accessToken = config('ministerio_saude.api.access_token');
+        $this->logRepository = $logRepository;
+    }
+
+    /**
+     * API 3.1 - Consultar Pedidos Farmanet
+     *
+     * Consulta pedidos no sistema GSNET do Ministério da Saúde
+     *
+     * @param ConsultarPedidosInput $input
+     * @return ServiceResponse
+     */
+    public function consultarPedidos(ConsultarPedidosInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.planejamento.endpoints.consultar_pedidos');
+            $url = "{$this->baseUrl}{$endpoint}";
+
+            $this->logInfo('PlanejamentoService@consultarPedidos - Iniciando consulta de pedidos via API real', [
+                'url' => $url,
+                'params' => $input->toArray()
+            ]);
+
+            $payload = $input->toQueryParams($this->accessToken);
+            $response = $this->client->get($url, [
+                'headers' => [
+                    'Accept' => 'application/json',
+                ],
+                'query' => $payload
+            ]);
+            $responseData = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('PlanejamentoService@consultarPedidos - Resposta recebida do Ministério', [
+                'status_code' => $response->getStatusCode(),
+                'total_itens_retornados' => count($responseData['Data'] ?? []),
+                'protocolo' => $responseData['Protocolo'] ?? null
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
+            }
+
+            $this->logRepository->storeConsultarPedidosFarmanet(
+                $input->toArray(),
+                array_merge($responseData, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse([
+                'result' => $responseData,
+                'total_itens' => count($responseData['Data'] ?? []),
+                'timestamp' => Carbon::now()->toISOString()
+            ], 'Consulta de pedidos realizada com sucesso via API real do Ministério.');
+        } catch (CustomMessageException $e) {
+            $this->logError('PlanejamentoService@consultarPedidos - Erro na consulta da API externa', $e, [
+                'url' => $url ?? 'URL não definida',
+                'params' => $input->toArray() ?? []
+            ])->setData($e->getData());
+            $this->logRepository->storeConsultarPedidosFarmanet(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
+
+            $this->logError('PlanejamentoService@consultarPedidos - Erro na requisição para o Ministério', $e, [
+                'status_code' => $statusCode,
+                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
+                'request_data' => $input
+            ]);
+
+            $this->logRepository->storeConsultarPedidosFarmanet(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+
+        } catch (\Exception $e) {
+            $this->logError('PlanejamentoService@consultarPedidos - Erro geral', $e, ['request_data' => $input]);
+            $this->logRepository->storeConsultarPedidosFarmanet(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse("Erro ao consultar pedidos: {$e->getMessage()}")
+                ->setNullData()
+                ->setErrorCode('INTERNAL_ERROR');
+        }
+        return $response;
+    }
+
+    /**
+     * API 3.2 - Atualizar Pedido Farmanet
+     *
+     * Atualiza pedido no sistema GSNET do Ministério da Saúde
+     *
+     * @param AtualizarPedidoInput $input
+     * @return ServiceResponse
+     */
+    public function atualizarPedido(AtualizarPedidoInput $input): ServiceResponse
+    {
+        try {
+            $endpoint = config('ministerio_saude.planejamento.endpoints.atualizar_pedido');
+            $url = "{$this->baseUrl}{$endpoint}";
+
+            $this->logInfo('PlanejamentoService@atualizarPedido - Iniciando atualização de pedido via API', [
+                'url' => $url,
+                'params' => $input->toArray()
+            ]);
+
+            $payload = $input->toQueryParams($this->accessToken);
+            $response = $this->client->put($url, [
+                'headers' => [
+                    'Accept' => 'application/json',
+                ],
+                'query' => $payload
+            ]);
+            $responseData = json_decode($response->getBody()->getContents(), true);
+
+            $this->logInfo('PlanejamentoService@atualizarPedido - Resposta recebida do Ministério', [
+                'status_code' => $response->getStatusCode()
+            ]);
+
+            if ($response->getStatusCode() !== 200) {
+                throw new CustomMessageException($responseData['Message'] ?? 'Erro na consulta', $responseData);
+            }
+
+            $this->logRepository->storeAtualizarPedidoFarmanet(
+                $input->toArray(),
+                array_merge($responseData, ['status_code' => $response->getStatusCode()]),
+                true
+            );
+
+            return $this->successResponse([
+                'result' => $responseData,
+                'timestamp' => Carbon::now()->toISOString()
+            ], 'Atualização de pedido realizada com sucesso via API real do Ministério.');
+        } catch (CustomMessageException $e) {
+            $this->logError('PlanejamentoService@atualizarPedido - Erro na atualização da API externa', $e, [
+                'url' => $url ?? 'URL não definida',
+                'params' => $input->toArray() ?? []
+            ])->setData($e->getData());
+            $this->logRepository->storeAtualizarPedidoFarmanet(
+                $input->toArray(),
+                $e->getData(),
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
+        } catch (RequestException $e) {
+            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
+            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
+
+            $this->logError('PlanejamentoService@atualizarPedido - Erro na requisição para o Ministério', $e, [
+                'status_code' => $statusCode,
+                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
+                'request_data' => $input
+            ]);
+            $this->logRepository->storeAtualizarPedidoFarmanet(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+
+            $response = $this->failureResponse($errorMessage)->setNullData();
+
+        } catch (\Exception $e) {
+            $this->logError('PlanejamentoService@atualizarPedido - Erro geral', $e, ['request_data' => $input]);
+            $this->logRepository->storeAtualizarPedidoFarmanet(
+                $input->toArray(),
+                null,
+                false,
+                $e->getMessage()
+            );
+            $response = $this->failureResponse("Erro ao atualizar pedido: {$e->getMessage()}")
+                ->setNullData()
+                ->setErrorCode('INTERNAL_ERROR');
+        }
+        return $response;
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Services/RecebimentoFaturasFarmanetService.php b/app/Domain/MinisterioSaude/Services/RecebimentoFaturasFarmanetService.php
index 5060063..0b389dd 100644
--- a/app/Domain/MinisterioSaude/Services/RecebimentoFaturasFarmanetService.php
+++ b/app/Domain/MinisterioSaude/Services/RecebimentoFaturasFarmanetService.php
@@ -107,50 +107,6 @@ class RecebimentoFaturasFarmanetService
         }
     }
 
-    /**
-                    'numero_fatura' => '2024090002',
-                    'id_gestor' => $params['id_gestor'] ?? '12345678901234567890',
-                    'ano_referencia' => $params['ano_referencia'] ?? '2024',
-                    'mes_referencia' => $params['mes_referencia'] ?? '9',
-                    'codigo_programa' => $params['codigo_programa'] ?? '5',
-                    'valor_total' => 8920.45,
-                    'status' => 'paga',
-                    'data_emissao' => '2024-09-15T10:30:00Z',
-                    'data_vencimento' => '2024-10-15T23:59:59Z',
-                    'data_pagamento' => '2024-09-25T14:22:33Z'
-                ]
-            ];
-
-            return [
-                'success' => true,
-                'data' => [
-                    'faturas' => $faturas,
-                    'total_faturas' => count($faturas),
-                    'valor_total_geral' => array_sum(array_column($faturas, 'valor_total')),
-                    'mes_referencia' => $params['mes_referencia'] ?? date('n'),
-                    'ano_referencia' => $params['ano_referencia'] ?? date('Y')
-                ],
-                'total_faturas' => count($faturas),
-                'timestamp' => Carbon::now()->toISOString(),
-                'simulated' => true,
-                'message' => 'Dados simulados - endpoint real não disponível'
-            ];
-
-        } catch (\Exception $e) {
-            Log::error('RecebimentoFaturasFarmanetService - Erro geral', [
-                'message' => $e->getMessage(),
-                'trace' => $e->getTraceAsString(),
-                'params' => $params
-            ]);
-
-            return [
-                'success' => false,
-                'error' => 'Erro interno: ' . $e->getMessage(),
-                'timestamp' => Carbon::now()->toISOString()
-            ];
-        }
-    }
-
     /**
      * Processa e armazena faturas recebidas da API
      *
diff --git a/app/Domain/MinisterioSaude/Services/StatusOperadorService.php b/app/Domain/MinisterioSaude/Services/StatusOperadorService.php
deleted file mode 100644
index 87f17f8..0000000
--- a/app/Domain/MinisterioSaude/Services/StatusOperadorService.php
+++ /dev/null
@@ -1,230 +0,0 @@
-<?php
-
-namespace Domain\MinisterioSaude\Services;
-
-use Domain\MinisterioSaude\Models\StatusOperador;
-use Illuminate\Support\Facades\Log;
-use Illuminate\Support\Facades\DB;
-use GuzzleHttp\Client;
-use GuzzleHttp\Exception\RequestException;
-
-class StatusOperadorService
-{
-    private $client;
-    private $baseUrl;
-    
-    public function __construct()
-    {
-        $this->client = new Client([
-            'timeout' => config('ministerio_saude.api.timeout', 30),
-            'verify' => false,
-        ]);
-        
-        $environment = config('ministerio_saude.api.environment', 'homolog');
-        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
-    }
-
-    /**
-     * Criar novo status tanto localmente quanto na API
-     */
-    public function criarStatus($idOrigem, $nomeStatus, $descricaoStatus)
-    {
-        try {
-            DB::connection('ministerio_saude_sp')->beginTransaction();
-
-            // Verificar se já existe
-            $statusExistente = StatusOperador::where('id_origem', $idOrigem)
-                ->orWhere('nome_status', $nomeStatus)
-                ->first();
-
-            if ($statusExistente) {
-                $errors = [];
-                if ($statusExistente->id_origem == $idOrigem) { // Mudança: usar == em vez de ===
-                    $errors[] = 'IdOrigem já conta na base';
-                }
-                if ($statusExistente->nome_status == $nomeStatus) { // Mudança: usar == em vez de ===
-                    $errors[] = 'NomeStatus já conta na base';
-                }
-
-                DB::connection('ministerio_saude_sp')->rollBack();
-                
-                // Garantir que sempre haja uma mensagem
-                $message = !empty($errors) ? implode(' e ', $errors) : 'Registro já existe na base';
-                
-                return [
-                    'success' => false,
-                    'message' => $message
-                ];
-            }
-
-            $apiResponse = null;
-            
-            // Fazer chamada HTTP real para a API do Ministério
-            try {
-                $url = $this->baseUrl . config('ministerio_saude.endpoints.inserir_status_op');
-                
-                $requestData = [
-                    'Data' => [
-                        'IdOrigem' => $idOrigem,
-                        'NomeStatus' => $nomeStatus,
-                        'DescricaoStatus' => $descricaoStatus,
-                    ],
-                    'AccessToken' => config('ministerio_saude.api.access_token')
-                ];
-                
-                Log::info('StatusOperadorService - Chamando API real do Ministério', [
-                    'url' => $url,
-                    'data' => collect($requestData)->except(['AccessToken'])->toArray() // Log sem o token
-                ]);
-                
-                $response = $this->client->post($url, [
-                    'json' => $requestData,
-                    'headers' => [
-                        'Content-Type' => 'application/json',
-                        'Accept' => 'application/json',
-                    ]
-                ]);
-                
-                $responseBody = json_decode($response->getBody()->getContents(), true);
-                
-                $apiResponse = [
-                    'success' => $response->getStatusCode() === 200,
-                    'data' => $responseBody,
-                    'message' => $responseBody['Message'] ?? 'Status inserido via API'
-                ];
-                
-                Log::info('StatusOperadorService - Resposta da API real recebida', [
-                    'status_code' => $response->getStatusCode(),
-                    'response' => $responseBody
-                ]);
-                
-            } catch (RequestException $e) {
-                $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
-                
-                Log::error('StatusOperadorService - Erro na API externa', [
-                    'error' => $errorMessage,
-                    'url' => $url ?? 'URL não definida',
-                    'data' => $requestData ?? []
-                ]);
-                
-                // Não usar API mockada - falhar se API real não funcionar
-                DB::connection('ministerio_saude_sp')->rollBack();
-                return [
-                    'success' => false,
-                    'message' => $errorMessage
-                ];
-            }
-
-            // Se a API respondeu com sucesso ou estamos em desenvolvimento, criar localmente
-            $status = StatusOperador::create([
-                'id_origem' => $idOrigem,
-                'nome_status' => $nomeStatus,
-                'descricao_status' => $descricaoStatus,
-                'flag_registro' => true
-            ]);
-
-            DB::connection('ministerio_saude_sp')->commit();
-
-            Log::info('StatusOperadorService - Status criado com sucesso', [
-                'status_id' => $status->id,
-                'id_origem' => $idOrigem,
-                'nome_status' => $nomeStatus,
-                'api_externa_usada' => true
-            ]);
-
-            return [
-                'success' => true,
-                'data' => $status,
-                'message' => 'Status incluído com Sucesso'
-            ];
-
-        } catch (\Exception $e) {
-            DB::connection('ministerio_saude_sp')->rollBack();
-            
-            Log::error('StatusOperadorService - Erro ao criar status', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return [
-                'success' => false,
-                'message' => 'Erro interno ao criar status: ' . $e->getMessage()
-            ];
-        }
-    }
-
-    /**
-     * Consultar status via API real do Ministério
-     */
-    public function consultarStatus($idStatus = null, $idOrigem = null, $nomeStatus = null)
-    {
-        try {
-            // Fazer chamada HTTP real para a API do Ministério (GET com query parameters)
-            $url = $this->baseUrl . config('ministerio_saude.endpoints.consultar_status_op');
-            
-            $queryParams = [
-                'AccessToken' => config('ministerio_saude.api.access_token')
-            ];
-            
-            // Adicionar parâmetros não nulos
-            if ($idStatus !== null) {
-                $queryParams['IdStatus'] = $idStatus;
-            }
-            if ($idOrigem !== null) {
-                $queryParams['IdOrigem'] = $idOrigem;
-            }
-            if ($nomeStatus !== null) {
-                $queryParams['NomeStatus'] = $nomeStatus;
-            }
-            
-            Log::info('StatusOperadorService - Consultando status via API real', [
-                'url' => $url,
-                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
-            ]);
-            
-            $response = $this->client->get($url, [
-                'query' => $queryParams,
-                'headers' => [
-                    'Accept' => 'application/json',
-                ]
-            ]);
-            
-            $responseBody = json_decode($response->getBody()->getContents(), true);
-            
-            Log::info('StatusOperadorService - Resposta da API real recebida', [
-                'status_code' => $response->getStatusCode(),
-                'total_records' => count($responseBody['Data'] ?? [])
-            ]);
-            
-            return [
-                'success' => $response->getStatusCode() === 200,
-                'data' => $responseBody,
-                'message' => $responseBody['Message'] ?? 'Consulta realizada com sucesso'
-            ];
-            
-        } catch (RequestException $e) {
-            $errorMessage = 'Erro na comunicação com API do Ministério: ' . $e->getMessage();
-            
-            Log::error('StatusOperadorService - Erro na consulta da API externa', [
-                'error' => $errorMessage,
-                'url' => $url ?? 'URL não definida'
-            ]);
-            
-            return [
-                'success' => false,
-                'message' => $errorMessage
-            ];
-            
-        } catch (\Exception $e) {
-            Log::error('StatusOperadorService - Erro ao consultar status', [
-                'error' => $e->getMessage(),
-                'trace' => $e->getTraceAsString()
-            ]);
-
-            return [
-                'success' => false,
-                'message' => 'Erro interno ao consultar status: ' . $e->getMessage()
-            ];
-        }
-    }
-}
diff --git a/app/Domain/MinisterioSaude/Traits/HasLog.php b/app/Domain/MinisterioSaude/Traits/HasLog.php
new file mode 100644
index 0000000..897bdca
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Traits/HasLog.php
@@ -0,0 +1,27 @@
+<?php
+
+namespace Domain\MinisterioSaude\Traits;
+
+use Illuminate\Support\Facades\Log;
+use Throwable;
+
+trait HasLog
+{
+    protected function logInfo(string $message, array $context): void
+    {
+        Log::info($message, $context);
+    }
+
+    protected function logError(string $message, Throwable $exception, array $context = []): void
+    {
+        Log::error($message, array_merge($context, [
+            'error' => $exception->getMessage(),
+            'trace' => $exception->getTraceAsString()
+        ]));
+    }
+
+    protected function logWarning(string $message, array $context): void
+    {
+        Log::warning($message, $context);
+    }
+}
diff --git a/app/Domain/MinisterioSaude/Traits/HasServiceResponse.php b/app/Domain/MinisterioSaude/Traits/HasServiceResponse.php
new file mode 100644
index 0000000..40193d8
--- /dev/null
+++ b/app/Domain/MinisterioSaude/Traits/HasServiceResponse.php
@@ -0,0 +1,49 @@
+<?php
+
+namespace Domain\MinisterioSaude\Traits;
+
+use Domain\MinisterioSaude\Helpers\ServiceResponse;
+use Throwable;
+
+trait HasServiceResponse
+{
+    protected function successResponse($data = null, string $message = ''): ServiceResponse
+    {
+        return ServiceResponse::success($data, $message);
+    }
+
+    protected function failureResponse(string $message, $data = null): ServiceResponse
+    {
+        return ServiceResponse::failure($message, $data);
+    }
+
+    protected function exceptionResponse(Throwable $exception, $data = null): ServiceResponse
+    {
+        return ServiceResponse::fromException($exception, $data);
+    }
+
+    protected function validationResponse(array $errors, string $message = 'Dados inválidos'): ServiceResponse
+    {
+        return ServiceResponse::validationError($errors, $message);
+    }
+
+    protected function notFoundResponse(string $resource = 'Recurso'): ServiceResponse
+    {
+        return ServiceResponse::notFound($resource);
+    }
+
+    protected function unauthorizedResponse(string $message = 'Não autorizado'): ServiceResponse
+    {
+        return ServiceResponse::unauthorized($message);
+    }
+
+    protected function forbiddenResponse(string $message = 'Acesso negado'): ServiceResponse
+    {
+        return ServiceResponse::forbidden($message);
+    }
+
+    protected function internalErrorResponse(string $message = 'Erro interno do servidor'): ServiceResponse
+    {
+        return ServiceResponse::internalError($message);
+    }
+}
diff --git a/config/database.php b/config/database.php
index b4fe95c..2e53b0c 100644
--- a/config/database.php
+++ b/config/database.php
@@ -89,6 +89,7 @@ return [
             'charset' => 'utf8',
             'prefix' => '',
             'prefix_indexes' => true,
+            'trust_server_certificate' => env('DB_TRUST_SERVER_CERTIFICATE', false),
         ],
 
         'esl_cloud' => [
diff --git a/config/ministerio_saude.php b/config/ministerio_saude.php
index f30f5cd..79ca92a 100644
--- a/config/ministerio_saude.php
+++ b/config/ministerio_saude.php
@@ -16,11 +16,11 @@ return [
             'production' => env('MS_SP_API_BASE_URL_PROD', 'https://operadorgsnethml.saude.sp.gov.br'),
             'homolog' => env('MS_SP_API_BASE_URL_HOMOLOG', 'https://operadorgsnethml.saude.sp.gov.br'),
         ],
-        'access_token' => env('MS_SP_API_ACCESS_TOKEN', ''),
         'system_code' => env('MS_SP_API_SYSTEM_CODE', ''),
         'environment' => env('MS_SP_API_ENVIRONMENT', 'homolog'), // 'production' ou 'homolog'
         'timeout' => env('MS_SP_API_TIMEOUT', 30),
         'force_external_in_dev' => env('MS_SP_API_FORCE_EXTERNAL_DEV', true), // Forçar uso de APIs reais
+        'access_token' => env('MS_SP_API_ACCESS_TOKEN', ''),
     ],
 
     'database' => [
@@ -29,36 +29,35 @@ return [
         'database' => env('DB_DATABASE_MS_SP', 'db_ms_sp'),
     ],
 
-    'endpoints' => [
-        // APIs de Status
-        'inserir_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/inserirStatusOp',
-        'consultar_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp',
-        
-        // APIs de Endereço
-        'consultar_endereco_local' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal',
-        
-        // APIs de Fatura GSNET
-        'consultar_faturas_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar',
-        'atualizar_status_fatura_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/AtualizarStatus',
-        
-        // APIs de Itens
-        'consultar_itens' => '/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens',
-    ],
-
     'farmanet' => [
         'base_url' => [
             'production' => env('MS_SP_FARMANET_BASE_URL_PROD', 'https://operadorgsnethml.saude.sp.gov.br'),
             'homolog' => env('MS_SP_FARMANET_BASE_URL_HOMOLOG', 'https://operadorgsnethml.saude.sp.gov.br'),
         ],
         'endpoints' => [
+            // APIs de Status
+            'inserir_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/inserirStatusOp',
+            'consultar_status_op' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarStatusOp',
+
+            // APIs de Endereço
+            'consultar_endereco_local' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultarEnderecoLocal',
+
+            // APIs de Fatura GSNET
+            'consultar_faturas_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/consultar',
+            'atualizar_status_fatura_gsnet' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura/atualizar',
+            'receber_faturas' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura',
+
+            // APIs de Itens
+            'consultar_itens' => '/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens',
+
             // APIs Farmanet - Pedidos
-            'consultar_pedidos' => '/ObterPedidosOperador',
+            'consultar_pedidos' => '/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos',
             'atualizar_pedido' => '/AtualizarPedidoOperador',
-            
+
             // APIs Farmanet - Faturas (URL corrigida)
             'consultar_faturas' => '/Prodesp.Gsnet.Operador.FaturaFarmanet.Servico/Fatura',
             'confirmar_recebimento_fatura' => '/ConfirmarRecebimentoFatura',
-            
+
             // APIs Farmanet - Preços
             'obter_preco_medio' => '/ObterPrecoMedioItens',
         ],
@@ -83,12 +82,20 @@ return [
         ],
         'endpoints' => [
             'consultar_itens' => '/Prodesp.Gsnet.Operador.Item.Servico/ConsultarItens',
+            'obter_preco_medio' => '/Prodesp.Gsnet.Operador.Item.Servico/ObterPrecoMedioUnitario',
         ],
         'timeout' => env('MS_SP_ITEMS_TIMEOUT', 60),
         'environment' => env('MS_SP_ITEMS_ENVIRONMENT', 'homolog'),
         'force_real_api' => env('MS_SP_ITEMS_FORCE_REAL', true), // Sempre usar APIs reais
     ],
 
+    'planejamento' => [
+        'endpoints' => [
+            'consultar_pedidos' => '/Prodesp.Gsnet.Operador.planejamento.servico/ConsultarPedidos',
+            'atualizar_pedido' => '/Prodesp.Gsnet.Operador.planejamento.servico/AtualizarPedidos',
+        ],
+    ],
+
     'sync' => [
         'enabled' => env('MS_SP_SYNC_ENABLED', true),
         'auto_sync_interval' => env('MS_SP_AUTO_SYNC_INTERVAL', 3600), // segundos
diff --git a/routes/MinisterioSaude/ministerio_saude_sp.php b/routes/MinisterioSaude/ministerio_saude_sp.php
index 738cff2..9e2e064 100644
--- a/routes/MinisterioSaude/ministerio_saude_sp.php
+++ b/routes/MinisterioSaude/ministerio_saude_sp.php
@@ -1,10 +1,11 @@
 <?php
 
+use Domain\MinisterioSaude\Controllers\PlanejamentoController;
 use Illuminate\Support\Facades\Route;
-use Domain\MinisterioSaude\Controllers\MinisterioSaudeController;
+use Domain\MinisterioSaude\Controllers\FaturaFarmanetController;
 use Domain\MinisterioSaude\Controllers\ConsultarPedidosFarmanetController;
 use Domain\MinisterioSaude\Controllers\AtualizarPedidoFarmanetController;
-use Domain\MinisterioSaude\Controllers\ConsultarItensController;
+use Domain\MinisterioSaude\Controllers\ItemController;
 use Domain\MinisterioSaude\Controllers\RecebimentoFaturasFarmanetController;
 use Domain\MinisterioSaude\Controllers\ObterPrecoMedioItensController;
 
@@ -19,102 +20,102 @@ use Domain\MinisterioSaude\Controllers\ObterPrecoMedioItensController;
 */
 
 Route::prefix('ministerio-saude/sp')->group(function () {
-    
+
     // === APIs PRINCIPAIS (MVP) ===
-    
+
     // API 1.1 - Inserir Status na Fatura
-    Route::post('/status/criar', [MinisterioSaudeController::class, 'inserirStatusFatura'])
+    Route::post('/status/criar', [FaturaFarmanetController::class, 'inserirStatusFatura'])
         ->name('ministerio-saude.inserir-status-fatura');
-    
+
     // API 1.2 - Consultar Status do Operador
-    Route::get('/status/consultar', [MinisterioSaudeController::class, 'consultarStatusOp'])
+    Route::get('/status/consultar', [FaturaFarmanetController::class, 'consultarStatusOp'])
         ->name('ministerio-saude.consultar-status-op');
-    
+
     // API 1.3 - Consultar Endereço Local
-    Route::get('/endereco/consultar', [MinisterioSaudeController::class, 'consultarEndereco'])
+    Route::get('/endereco/consultar', [FaturaFarmanetController::class, 'consultarEndereco'])
         ->name('ministerio-saude.consultar-endereco');
-    
+
     // API 1.4 - Consultar Itens (Recebe)
-    Route::post('/itens/consultar', [ConsultarItensController::class, 'consultarItens'])
+    Route::post('/itens/consultar', [ItemController::class, 'consultarItens'])
         ->name('ministerio-saude.consultar-itens');
-    
+
     // === NOVAS APIs - FATURAS (TESTE SEM AUTH) ===
-    
+
     // API 2.1 - Consultar Faturas (Recebe) - TESTE
-    Route::get('/faturas/consultar-teste', [MinisterioSaudeController::class, 'consultarFaturas'])
+    Route::get('/faturas/consultar-teste', [FaturaFarmanetController::class, 'consultarFaturas'])
         ->name('ministerio-saude.consultar-faturas-teste');
-    
+
     // API 2.2 - Atualizar Status da Fatura (Envio) - TESTE
-    Route::post('/faturas/status/atualizar-teste', [MinisterioSaudeController::class, 'atualizarStatusFatura'])
+    Route::post('/faturas/status/atualizar-teste', [FaturaFarmanetController::class, 'atualizarStatusFatura'])
         ->name('ministerio-saude.atualizar-status-fatura-teste');
-    
+
     // === NOVAS APIs - FATURAS ===
-    
+
     // API 2.1 - Consultar Faturas (Recebe)
-    Route::post('/faturas/consultar', [MinisterioSaudeController::class, 'consultarFaturas'])
+    Route::get('/faturas/consultar', [FaturaFarmanetController::class, 'consultarFaturas'])
         ->name('ministerio-saude.consultar-faturas');
-    
+
     // API 2.2 - Atualizar Status da Fatura (Envio)
-    Route::post('/faturas/status/atualizar', [MinisterioSaudeController::class, 'atualizarStatusFatura'])
+    Route::put('/faturas/status/atualizar', [FaturaFarmanetController::class, 'atualizarStatusFatura'])
         ->name('ministerio-saude.atualizar-status-fatura');
 
     // === NOVAS APIs - FARMANET (3.1 - 3.4) ===
-    
+
     // API 3.1 - Consultar Pedidos Farmanet (Recebe)
-    Route::post('/pedidos-farmanet/consultar', [ConsultarPedidosFarmanetController::class, 'consultarPedidos'])
+    Route::get('/pedidos-farmanet/consultar', [PlanejamentoController::class, 'consultarPedidos'])
         ->name('ministerio-saude.consultar-pedidos-farmanet');
-    
+
     Route::get('/pedidos-farmanet/locais', [ConsultarPedidosFarmanetController::class, 'buscarPedidosLocais'])
         ->name('ministerio-saude.buscar-pedidos-farmanet-locais');
-    
+
     Route::get('/pedidos-farmanet/info', [ConsultarPedidosFarmanetController::class, 'info'])
         ->name('ministerio-saude.pedidos-farmanet-info');
-    
+
     // API 3.2 - Atualizar Pedidos Farmanet (Envio)
-    Route::put('/pedidos-farmanet/atualizar', [AtualizarPedidoFarmanetController::class, 'atualizarPedido'])
+    Route::put('/pedidos-farmanet/atualizar', [PlanejamentoController::class, 'atualizarPedido'])
         ->name('ministerio-saude.atualizar-pedido-farmanet');
-    
+
     Route::post('/pedidos-farmanet/validar-atualizacao', [AtualizarPedidoFarmanetController::class, 'validarAtualizacao'])
         ->name('ministerio-saude.validar-atualizacao-pedido-farmanet');
-    
+
     Route::get('/pedidos-farmanet/status', [AtualizarPedidoFarmanetController::class, 'listarStatus'])
         ->name('ministerio-saude.listar-status-pedidos-farmanet');
-    
+
     Route::get('/pedidos-farmanet/atualizar/info', [AtualizarPedidoFarmanetController::class, 'info'])
         ->name('ministerio-saude.atualizar-pedido-farmanet-info');
-    
+
     // API 3.3 - Recebimento Faturas Farmanet (Recebe)
-    Route::post('/faturas-farmanet/consultar', [RecebimentoFaturasFarmanetController::class, 'consultarFaturas'])
-        ->name('ministerio-saude.consultar-faturas-farmanet');
-    
+    Route::put('/faturas-farmanet/receber', [FaturaFarmanetController::class, 'receberFaturas'])
+        ->name('ministerio-saude.receber-faturas-farmanet');
+
     Route::post('/faturas-farmanet/confirmar-recebimento', [RecebimentoFaturasFarmanetController::class, 'confirmarRecebimento'])
         ->name('ministerio-saude.confirmar-recebimento-fatura-farmanet');
-    
+
     Route::get('/faturas-farmanet/locais', [RecebimentoFaturasFarmanetController::class, 'buscarFaturasLocais'])
         ->name('ministerio-saude.buscar-faturas-farmanet-locais');
-    
+
     Route::get('/faturas-farmanet/status', [RecebimentoFaturasFarmanetController::class, 'listarStatus'])
         ->name('ministerio-saude.listar-status-faturas-farmanet');
-    
+
     Route::get('/faturas-farmanet/info', [RecebimentoFaturasFarmanetController::class, 'info'])
         ->name('ministerio-saude.faturas-farmanet-info');
-    
+
     // API 3.4 - Obter Preço Médio de Itens (Recebe)
-    Route::post('/preco-medio-itens/obter', [ObterPrecoMedioItensController::class, 'obterPrecoMedio'])
+    Route::post('/preco-medio-itens/obter', [ItemController::class, 'obterPrecoMedio'])
         ->name('ministerio-saude.obter-preco-medio-itens');
-    
+
     Route::get('/preco-medio-itens/locais', [ObterPrecoMedioItensController::class, 'buscarPrecosLocais'])
         ->name('ministerio-saude.buscar-precos-locais');
-    
+
     Route::post('/preco-medio-itens/estatisticas', [ObterPrecoMedioItensController::class, 'obterEstatisticas'])
         ->name('ministerio-saude.obter-estatisticas-precos');
-    
+
     Route::post('/preco-medio-itens/comparar', [ObterPrecoMedioItensController::class, 'compararPrecos'])
         ->name('ministerio-saude.comparar-precos');
-    
+
     Route::get('/preco-medio-itens/programas', [ObterPrecoMedioItensController::class, 'listarProgramas'])
         ->name('ministerio-saude.listar-programas-precos');
-    
+
     Route::get('/preco-medio-itens/info', [ObterPrecoMedioItensController::class, 'info'])
         ->name('ministerio-saude.preco-medio-itens-info');
 
