<?php

namespace Domain\MinisterioSaude\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StatusOperador extends Model
{
    use SoftDeletes;

    protected $connection = 'ministerio_saude_sp';
    protected $table = 'status_operador';
    
    protected $fillable = [
        'id_origem',
        'nome_status',
        'descricao_status',
        'flag_registro',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'flag_registro' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Scope para buscar por ID de origem
     */
    public function scopeByIdOrigem($query, $idOrigem)
    {
        return $query->where('id_origem', $idOrigem);
    }

    /**
     * Scope para buscar por nome do status
     */
    public function scopeByNomeStatus($query, $nomeStatus)
    {
        return $query->where('nome_status', $nomeStatus);
    }

    /**
     * Scope para registros ativos
     */
    public function scopeAtivos($query)
    {
        return $query->where('flag_registro', true);
    }
}
