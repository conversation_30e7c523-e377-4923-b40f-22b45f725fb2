<?php

use Stateful\IceExchange\Controllers\CustomersController;
use Stateful\IceExchange\Controllers\CustomerDeadlinesController;
use Stateful\IceExchange\Controllers\VolumeEventsController;
use Stateful\IceExchange\Controllers\IceItemsController;
use Stateful\IceExchange\Controllers\IceWarehousesController;
use Stateful\IceExchange\Controllers\VolumesController;
use Stateful\IceExchange\Controllers\InvoiceController;
use Stateful\IceExchange\Controllers\LabelsController;
use Stateful\IceExchange\Controllers\ColdChamberController;
use Illuminate\Support\Facades\Route;

Route::prefix('ice-exchange')
    ->group(function () {
        
        // Rotas para clientes
        Route::prefix('customers')->group(function () {
            Route::get('/', [CustomersController::class, 'index']);
            Route::get('/search', [CustomersController::class, 'search']);
            Route::get('/search-cnpj', [CustomersController::class, 'searchByCnpj']);
            Route::post('/', [CustomersController::class, 'store']);
            Route::get('/{id}', [CustomersController::class, 'show']);
            Route::put('/{id}', [CustomersController::class, 'update']);
            Route::delete('/{id}', [CustomersController::class, 'destroy']);
        });

        // Rotas para prazos de vencimento por cliente
        Route::prefix('customer-deadlines')->group(function () {
            Route::get('/', [CustomerDeadlinesController::class, 'index']);
            Route::post('/', [CustomerDeadlinesController::class, 'store']);
            Route::put('/{id}', [CustomerDeadlinesController::class, 'update']);
            Route::delete('/{id}', [CustomerDeadlinesController::class, 'destroy']);
        });

        // Rotas para eventos de volumes
        Route::prefix('volume-events')->group(function () {
            Route::get('/', [VolumeEventsController::class, 'index']);
            Route::post('/', [VolumeEventsController::class, 'store']);
            Route::post('/batch', [VolumeEventsController::class, 'storeBatch']);
            Route::put('/', [VolumeEventsController::class, 'update']);
            Route::delete('/', [VolumeEventsController::class, 'delete']);
            Route::get('/audit', [VolumeEventsController::class, 'audit']);
        });

        // Rotas para volumes
        Route::prefix('volumes')->group(function () {
            Route::get('/{volumeCode}', [VolumesController::class, 'show']);
        });

        // Rotas para invoices
        Route::prefix('invoices')->group(function () {
            Route::get('/{invoiceKey}/volumes', [InvoiceController::class, 'getVolumes']);
        });

        // Rotas para tipos de gelo
        Route::prefix('ice-items')->group(function () {
            Route::get('/', [IceItemsController::class, 'index']);
            Route::post('/', [IceItemsController::class, 'store']);
            Route::put('/{id}', [IceItemsController::class, 'update']);
            Route::delete('/{id}', [IceItemsController::class, 'destroy']);
        });

        // Rotas para armazéns/câmaras frias
        Route::prefix('ice-warehouses')->group(function () {
            Route::get('/', [IceWarehousesController::class, 'index']);
            Route::post('/', [IceWarehousesController::class, 'store']);
            Route::put('/{id}', [IceWarehousesController::class, 'update']);
            Route::delete('/{id}', [IceWarehousesController::class, 'destroy']);
        });

        // Rotas para etiquetas
        Route::prefix('labels')->group(function () {
            Route::post('/search-volumes', [LabelsController::class, 'searchVolumes']);
            Route::post('/generate', [LabelsController::class, 'generateLabels']);
        });

        // Rotas para controle de câmara fria
        Route::prefix('cold-chamber')->group(function () {
            Route::post('/entry', [ColdChamberController::class, 'registerEntry']);
            Route::post('/exit', [ColdChamberController::class, 'registerExit']);
            Route::get('/volume-history/{volume_id}', [ColdChamberController::class, 'getVolumeHistory']);
            Route::get('/warehouse-volumes/{warehouse_id}', [ColdChamberController::class, 'getWarehouseVolumes']);
        });
    }); 