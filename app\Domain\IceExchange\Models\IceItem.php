<?php

namespace Domain\IceExchange\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class IceItem extends Model
{
    protected $connection = null; // usar conexão padrão (db_docs)
    protected $table = 'ice_change_items';

    protected $fillable = [
        'name',
        'description',
        'active',
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    public function volumeEvents(): HasMany
    {
        return $this->hasMany(VolumeEvent::class, 'ice_type_id');
    }

    /**
     * Escopo para filtrar apenas itens ativos
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }
} 