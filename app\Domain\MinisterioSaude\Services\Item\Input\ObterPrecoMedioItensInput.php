<?php

namespace Domain\MinisterioSaude\Services\Item\Input;

class ObterPrecoMedioItensInput
{
    /** @var ObterPrecoMedioListaItensInput[] */
    public $listaItens;

    /**
     * @param ObterPrecoMedioListaItensInput[] $listaItens
     */
    public function __construct(array $listaItens)
    {
        $this->listaItens = $listaItens;
    }

    public static function fromArray(array $data): ObterPrecoMedioItensInput
    {
        $listaItens = [];

        if (isset($data['lista_itens']) && is_array($data['lista_itens'])) {
            foreach ($data['lista_itens'] as $item) {
                if (isset($item['codigo_item'])) {
                    $listaItens[] = ObterPrecoMedioListaItensInput::fromArray($item);
                }
            }
        }

        return new self($listaItens);
    }

    public function toArray(): array
    {
        $listaItensArray = [];
        foreach ($this->listaItens as $item) {
            $listaItensArray[] = $item->toArray();
        }

        return [
            'lista_itens' => $listaItensArray
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        $listaItensArray = [];
        foreach ($this->listaItens as $item) {
            $listaItensArray[] = $item->toArray();
        }

        return [
            'Data' => [
                'ListaItens' => $listaItensArray
            ],
            'AccessToken' => $accessToken
        ];
    }
}

class ObterPrecoMedioListaItensInput
{
    public ?int $codigoItem;

    public function __construct(int $codigoItem)
    {
        $this->codigoItem = $codigoItem;
    }

    public static function fromArray(array $data): self
    {
        return new self($data['codigo_item'] ?? null);
    }

    public function toArray(): array
    {
        return [
            'CodigoItem' => $this->codigoItem
        ];
    }
}
