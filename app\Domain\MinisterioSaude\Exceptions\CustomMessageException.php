<?php

namespace Domain\MinisterioSaude\Exceptions;

use Exception;
use Throwable;

class CustomMessageException extends Exception
{
    private $data;
    private string $errorCode;

    public function __construct(string $message, $data, string $errorCode = "INVALID_OPERATION", int $code = 400, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->data = $data;
        $this->errorCode = $errorCode;
    }

    public function setData($data)
    {
        $this->data = $data;
        return $this;
    }

    public function getData()
    {
        return $this->data;
    }

    public function setErrorCode(string $code)
    {
        $this->errorCode = $code;
        return $this;
    }

    public function getErrorCode()
    {
        return $this->errorCode;
    }
}
