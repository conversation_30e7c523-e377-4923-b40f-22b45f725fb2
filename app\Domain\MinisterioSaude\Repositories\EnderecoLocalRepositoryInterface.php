<?php

namespace Domain\MinisterioSaude\Repositories;

use Domain\MinisterioSaude\Models\EnderecoLocal;
use Illuminate\Database\Eloquent\Collection;

interface EnderecoLocalRepositoryInterface
{
    public function buscarPorGestorELocal(string $idGestor, string $idLocal): ?EnderecoLocal;
    
    public function buscarTodos(): Collection;
    
    public function buscarAtivos(): Collection;
    
    public function criar(array $dados): EnderecoLocal;
    
    public function atualizar(EnderecoLocal $endereco, array $dados): EnderecoLocal;
    
    public function buscarSemGeolocalizacao(int $limite = 50): Collection;
    
    public function buscarPorMunicipio(string $municipio): Collection;
    
    public function buscarPorUf(string $uf): Collection;
}
