<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIceExchangeFinalTables extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Criar tabela de prazos de clientes (customers_service)
        if (!Schema::connection('customers_service')->hasTable('ice_change_deadlines')) {
            Schema::connection('customers_service')->create('ice_change_deadlines', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('name');
            $table->integer('hours');
            $table->timestamps();
            
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->unique(['customer_id', 'name']);
            });
        }

        // 2. Criar tabela de tipos de gelo
        if (!Schema::hasTable('ice_change_items')) {
            Schema::create('ice_change_items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
            });
        }

        // 3. Criar tabela de armazéns
        if (!Schema::hasTable('ice_change_warehouse')) {
            Schema::create('ice_change_warehouse', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            $table->timestamps();
            });
        }

        // 4. Criar tabela de eventos de volumes (estrutura final)
        if (!Schema::hasTable('ice_change_volume_events')) {
            Schema::create('ice_change_volume_events', function (Blueprint $table) {
            $table->id();
            $table->enum('event_type', ['filling', 'ice_change', 'cold_chamber_entry', 'cold_chamber_exit']);
            $table->datetime('event_date');
            $table->datetime('due_date')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('ice_type_id')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->integer('deadline_hours');
            $table->boolean('emit_label')->default(false);
            $table->unsignedBigInteger('notfis_volume_id');
            $table->timestamps();
            
            $table->foreign('ice_type_id')->references('id')->on('ice_change_items')->onDelete('set null');
            $table->foreign('warehouse_id')->references('id')->on('ice_change_warehouse')->onDelete('set null');
            $table->foreign('notfis_volume_id')->references('id')->on('db_docs.dbo.notfis_volumes')->onDelete('cascade');
            
            $table->index(['event_type', 'event_date']);
            $table->index('user_id');
            });
        }

        // 5. Criar tabela de auditoria de eventos (estrutura final)
        if (!Schema::hasTable('ice_change_volume_events_audit')) {
            Schema::create('ice_change_volume_events_audit', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('volume_event_id');
            $table->enum('event_type', ['filling', 'ice_change', 'cold_chamber_entry', 'cold_chamber_exit']);
            $table->datetime('event_date');
            $table->datetime('due_date')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('ice_type_id')->nullable();
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->integer('deadline_hours');
            $table->boolean('emit_label')->default(false);
            $table->unsignedBigInteger('notfis_volume_id');
            $table->unsignedBigInteger('user_id_audit')->nullable();
            $table->string('operation_type')->nullable();
            $table->timestamps();
            
            $table->foreign('volume_event_id')->references('id')->on('ice_change_volume_events')->onDelete('cascade');
            $table->foreign('ice_type_id')->references('id')->on('ice_change_items')->onDelete('set null');
            $table->foreign('warehouse_id')->references('id')->on('ice_change_warehouse')->onDelete('set null');
            $table->foreign('notfis_volume_id')->references('id')->on('db_docs.dbo.notfis_volumes')->onDelete('no action');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ice_change_volume_events_audit');
        Schema::dropIfExists('ice_change_volume_events');
        Schema::dropIfExists('ice_change_warehouse');
        Schema::dropIfExists('ice_change_items');
        Schema::connection('customers_service')->dropIfExists('ice_change_deadlines');
    }
}
