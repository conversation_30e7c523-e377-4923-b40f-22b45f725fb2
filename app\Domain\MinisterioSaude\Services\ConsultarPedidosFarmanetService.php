<?php

namespace Domain\MinisterioSaude\Services;

use App\Models\PedidoFarmanet;
use App\Models\PedidoFarmanetItem;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ConsultarPedidosFarmanetService
{
    private $client;
    private $baseUrl;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.farmanet.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.farmanet.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.farmanet.base_url.{$environment}");
    }

    /**
     * Consulta pedidos do Farmanet no Ministério da Saúde
     *
     * @param array $params
     * @return array
     */
    public function consultarPedidos(array $params): array
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_pedidos');
            $access_token = config('ministerio_saude.api.access_token');
            $url = $this->baseUrl . $endpoint;

            Log::info('ConsultarPedidosFarmanetService - Iniciando consulta', [
                'url' => $url,
                'params' => $params
            ]);

            $response = $this->client->get($url, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'query' => [
                    'IdGestor' => $params['id_gestor'],
                    'CodigoPrograma' => $params['codigo_programa'] ?? null,
                    'AnoReferencia' => $params['ano_referencia'],
                    'MesReferencia' => $params['mes_referencia'] ?? null,
                    'AnoPeriodoReferencia' => $params['ano_periodo_referencia'] ?? null,
                    'AccessToken' => $access_token
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            Log::info('ConsultarPedidosFarmanetService - Resposta recebida', [
                'status_code' => $response->getStatusCode(),
                'total_pedidos' => count($data['Data'] ?? [])
            ]);

            // Processar e armazenar os pedidos localmente
            if (isset($data['Data']) && is_array($data['Data'])) {
                $this->processarEArmazenarPedidos($data['Data'], $params);
            }

            return [
                'success' => true,
                'data' => $data,
                'total_pedidos' => count($data['Data'] ?? []),
                'timestamp' => Carbon::now()->toISOString()
            ];

        } catch (RequestException $e) {
            Log::error('ConsultarPedidosFarmanetService - Erro na requisição', [
                'message' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'params' => $params
            ]);

            return [
                'success' => false,
                'error' => 'Erro ao consultar pedidos Farmanet: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        } catch (\Exception $e) {
            Log::error('ConsultarPedidosFarmanetService - Erro geral', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $params
            ]);

            return [
                'success' => false,
                'error' => 'Erro interno: ' . $e->getMessage(),
                'timestamp' => Carbon::now()->toISOString()
            ];
        }
    }

    /**
     * Processa e armazena pedidos recebidos da API
     *
     * @param array $pedidos
     * @param array $params
     * @return void
     */
    private function processarEArmazenarPedidos(array $pedidos, array $params): void
    {
        foreach ($pedidos as $pedidoData) {
            try {
                // Buscar ou criar o pedido
                $pedido = PedidoFarmanet::updateOrCreate(
                    [
                        'id_pedido_ms' => $pedidoData['id_pedido'],
                        'id_gestor' => $params['id_gestor']
                    ],
                    [
                        'codigo_programa' => $pedidoData['codigo_programa'] ?? $params['codigo_programa'],
                        'numero_pedido' => $pedidoData['numero_pedido'] ?? null,
                        'status_pedido' => $pedidoData['status'] ?? 1,
                        'data_pedido' => isset($pedidoData['data_pedido']) ? Carbon::parse($pedidoData['data_pedido']) : null,
                        'data_entrega_prevista' => isset($pedidoData['data_entrega_prevista']) ? Carbon::parse($pedidoData['data_entrega_prevista']) : null,
                        'valor_total' => $pedidoData['valor_total'] ?? null,
                        'observacoes' => $pedidoData['observacoes'] ?? null,
                        'data_ultima_atualizacao' => Carbon::now(),
                        'dados_originais' => json_encode($pedidoData)
                    ]
                );

                // Processar itens do pedido
                if (isset($pedidoData['itens']) && is_array($pedidoData['itens'])) {
                    $this->processarItensPedido($pedido, $pedidoData['itens']);
                }

                Log::info('Pedido Farmanet processado', [
                    'pedido_id' => $pedido->id,
                    'id_pedido_ms' => $pedido->id_pedido_ms,
                    'total_itens' => count($pedidoData['itens'] ?? [])
                ]);

            } catch (\Exception $e) {
                Log::error('Erro ao processar pedido Farmanet', [
                    'pedido_data' => $pedidoData,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Processa itens de um pedido
     *
     * @param PedidoFarmanet $pedido
     * @param array $itens
     * @return void
     */
    private function processarItensPedido(PedidoFarmanet $pedido, array $itens): void
    {
        // Limpar itens existentes para recriar
        $pedido->itens()->delete();

        foreach ($itens as $itemData) {
            try {
                PedidoFarmanetItem::create([
                    'pedido_farmanet_id' => $pedido->id,
                    'codigo_medicamento' => $itemData['codigo_medicamento'] ?? null,
                    'nome_medicamento' => $itemData['nome_medicamento'] ?? null,
                    'codigo_farmaco' => $itemData['codigo_farmaco'] ?? null,
                    'quantidade_solicitada' => $itemData['quantidade_solicitada'] ?? 0,
                    'quantidade_aprovada' => $itemData['quantidade_aprovada'] ?? null,
                    'quantidade_entregue' => $itemData['quantidade_entregue'] ?? null,
                    'valor_unitario' => $itemData['valor_unitario'] ?? null,
                    'valor_total' => $itemData['valor_total'] ?? null,
                    'status_item' => $itemData['status'] ?? 1,
                    'observacoes' => $itemData['observacoes'] ?? null,
                    'dados_originais' => json_encode($itemData)
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao processar item do pedido Farmanet', [
                    'pedido_id' => $pedido->id,
                    'item_data' => $itemData,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Busca pedidos locais com filtros
     *
     * @param array $filters
     * @return array
     */
    public function buscarPedidosLocais(array $filters = []): array
    {
        $query = PedidoFarmanet::with('itens');

        if (isset($filters['id_gestor'])) {
            $query->where('id_gestor', $filters['id_gestor']);
        }

        if (isset($filters['codigo_programa'])) {
            $query->where('codigo_programa', $filters['codigo_programa']);
        }

        if (isset($filters['ano_referencia'])) {
            $query->whereYear('data_pedido', $filters['ano_referencia']);
        }

        if (isset($filters['mes_referencia'])) {
            $query->whereMonth('data_pedido', $filters['mes_referencia']);
        }

        if (isset($filters['status_pedido'])) {
            $query->where('status_pedido', $filters['status_pedido']);
        }

        $pedidos = $query->orderBy('data_pedido', 'desc')->get();

        return [
            'success' => true,
            'pedidos' => $pedidos->toArray(),
            'total' => $pedidos->count(),
            'timestamp' => Carbon::now()->toISOString()
        ];
    }
}
