<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasFarmanetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_farmanet', function (Blueprint $table) {
            $table->id();
            $table->date('dt_documento')->comment('Data de emissão do documento');
            $table->string('nr_documento', 11)->comment('Número do documento do OL');
            $table->decimal('vl_documento', 15, 2)->comment('Valor do documento');
            $table->integer('id_programa_saude')->comment('Código do programa saúde');
            $table->string('cd_pedido', 13)->comment('Código do pedido Farmanet');
            $table->integer('id_local_destino')->nullable()->comment('ID local destino');
            
            // Campos de retorno da API
            $table->string('documento_token', 40)->nullable()->comment('Token único emitido por Documento');
            $table->datetime('data_documento')->nullable()->comment('Data retornada pelo GSNET');
            $table->string('numero_documento', 11)->nullable()->comment('Número gerado pelo GSNET');
            $table->string('serie_documento', 40)->nullable()->comment('Série do documento');
            
            // Controle
            $table->boolean('enviado_gsnet')->default(false)->comment('Se foi enviado para o GSNET');
            $table->datetime('data_envio')->nullable()->comment('Data do envio');
            $table->text('resposta_gsnet')->nullable()->comment('Resposta completa do GSNET');
            $table->boolean('ativo')->default(true);
            $table->timestamps();
            
            // Indexes
            $table->index(['cd_pedido']);
            $table->index(['id_programa_saude']);
            $table->index(['enviado_gsnet']);
            $table->unique(['nr_documento', 'id_programa_saude']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_farmanet');
    }
}
