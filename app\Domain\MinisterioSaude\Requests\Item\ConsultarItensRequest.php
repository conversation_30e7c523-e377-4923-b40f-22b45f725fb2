<?php

namespace Domain\MinisterioSaude\Requests\Item;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class ConsultarItensRequest extends ApiFormRequest
{
    public function rules(): array
    {
        return [
            'lista_itens' => 'required|array|min:1|max:100',
            'lista_itens.*.codigo_material' => 'required|numeric|digits_between:1,11'
        ];
    }

    public function messages(): array
    {
        return [
            'lista_itens.required' => 'A lista de itens é obrigatória',
            'lista_itens.array' => 'A lista de itens deve ser um array',
            'lista_itens.min' => 'A lista de itens deve ter no mínimo 1 item',
            'lista_itens.max' => 'A lista de itens deve ter no máximo 100 itens',
            'lista_itens.*.codigo_material.required' => 'O código do material é obrigatório',
            'lista_itens.*.codigo_material.numeric' => 'O código do material deve ser um número',
            'lista_itens.*.codigo_material.digits_between' => 'O código do material deve ter entre 1 e 11 dígitos'
        ];
    }

    public function attributes(): array
    {
        return [
            'lista_itens' => 'Lista de Itens',
            'lista_itens.*.codigo_material' => 'Código do Material'
        ];
    }
}
