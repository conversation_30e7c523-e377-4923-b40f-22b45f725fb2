<?php

namespace Domain\MinisterioSaude\Services\Planejamento\Input;

class ConsultarPedidosInput
{
    public ?int $codigoPrograma;
    public ?int $anoReferencia;
    public ?int $mesReferencia;
    public ?int $anoPeriodoReferencia;
    public ?int $idGestor;

    public function __construct(?int $codigoPrograma, ?int $anoReferencia, ?int $mesReferencia, ?int $anoPeriodoReferencia, ?int $idGestor)
    {
        $this->codigoPrograma = $codigoPrograma;
        $this->anoReferencia = $anoReferencia;
        $this->mesReferencia = $mesReferencia;
        $this->anoPeriodoReferencia = $anoPeriodoReferencia;
        $this->idGestor = $idGestor;
    }

    public static function fromArray(array $data): ConsultarPedidosInput
    {
        return new self(
            $data['codigo_programa'] ?? null,
            $data['ano_referencia'] ?? null,
            $data['mes_referencia'] ?? null,
            $data['ano_periodo_referencia'] ?? null,
            $data['id_gestor'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'codigo_programa' => $this->codigoPrograma,
            'ano_referencia' => $this->anoReferencia,
            'mes_referencia' => $this->mesReferencia,
            'ano_periodo_referencia' => $this->anoPeriodoReferencia,
            'id_gestor' => $this->idGestor
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'CodigoPrograma' => $this->codigoPrograma,
            'AnoReferencia' => $this->anoReferencia,
            'MesReferencia' => $this->mesReferencia,
            'AnoPeriodoReferencia' => $this->anoPeriodoReferencia,
            'IdGestor' => $this->idGestor,
            'AccessToken' => $accessToken
        ];
    }
}
