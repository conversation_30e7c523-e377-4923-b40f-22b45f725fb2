<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class ClearMockupDataFromMinisterioTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Limpar todas as tabelas que contêm dados mockados do Ministério da Saúde
        // Isso permitirá que todas as APIs consumam apenas dados reais
        
        // SQL Server não precisa de FOREIGN_KEY_CHECKS, usa NOCHECK CONSTRAINT
        
        // Tabelas de Status
        if (Schema::hasTable('status_operador')) {
            DB::table('status_operador')->delete();
        }
        
        // Tabelas de Endereços
        if (Schema::hasTable('enderecos_ministerio_saude')) {
            DB::table('enderecos_ministerio_saude')->delete();
        }
        
        // Tabelas de Itens
        if (Schema::hasTable('itens_material')) {
            DB::table('itens_material')->delete();
        }
        
        // Tabelas de Faturas GSNET - ordem para evitar FK constraints
        if (Schema::hasTable('faturas_gsnet_status_controle')) {
            DB::table('faturas_gsnet_status_controle')->delete();
        }
        if (Schema::hasTable('faturas_gsnet_itens')) {
            DB::table('faturas_gsnet_itens')->delete();
        }
        if (Schema::hasTable('faturas_gsnet')) {
            DB::table('faturas_gsnet')->delete();
        }
        
        // Tabelas de Pedidos Farmanet
        if (Schema::hasTable('pedidos_farmanet_itens')) {
            DB::table('pedidos_farmanet_itens')->delete();
        }
        if (Schema::hasTable('pedidos_farmanet')) {
            DB::table('pedidos_farmanet')->delete();
        }
        
        // Tabelas de Faturas Farmanet
        if (Schema::hasTable('faturas_farmanet_itens')) {
            DB::table('faturas_farmanet_itens')->delete();
        }
        if (Schema::hasTable('faturas_farmanet')) {
            DB::table('faturas_farmanet')->delete();
        }
        
        // Tabelas de Preços Médios
        if (Schema::hasTable('preco_medio_itens')) {
            DB::table('preco_medio_itens')->delete();
        }
        
        echo "✅ Todas as tabelas com dados mockados foram limpas.\n";
        echo "🚀 Agora todas as APIs vão consumir apenas dados reais do Ministério da Saúde.\n";
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Não há necessidade de reverter esta operação
        // Os dados mockados não eram importantes para manter
        echo "⚠️  Esta migration não pode ser revertida pois limpa dados mockados.\n";
    }
}
