<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Domain\MinisterioSaude\Models\StatusOperador;

class MinisterioSaudeTestController extends Controller
{
    /**
     * Teste simples para verificar se o módulo está funcionando
     */
    public function teste(): JsonResponse
    {
        try {
            // Testar conexão com o banco
            $count = StatusOperador::count();
            
            return response()->json([
                'success' => true,
                'message' => 'Módulo Ministério da Saúde SP funcionando!',
                'data' => [
                    'status_count' => $count,
                    'database_connection' => 'ministerio_saude_sp',
                    'timestamp' => now()->format('Y-m-d H:i:s')
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro no módulo: ' . $e->getMessage(),
                'error' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Listar status sem autenticação (para teste)
     */
    public function listarStatusTeste(): JsonResponse
    {
        try {
            $statuses = StatusOperador::ativos()
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
            
            return response()->json([
                'success' => true,
                'message' => 'Status listados com sucesso',
                'data' => $statuses->map(function ($status) {
                    return [
                        'id' => $status->id,
                        'id_origem' => $status->id_origem,
                        'nome_status' => $status->nome_status,
                        'descricao_status' => $status->descricao_status,
                        'created_at' => $status->created_at->format('Y-m-d H:i:s')
                    ];
                })
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao listar status: ' . $e->getMessage()
            ], 500);
        }
    }
}
