<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input;

class ConsultaFaturaInput
{
    public string $idGestor;
    public ?string $anoReferencia;
    public ?string $mesReferencia;
    public ?string $codigoPrograma;
    public ?string $dataInicio;
    public ?string $dataFim;
    public ?string $localOrigemId;
    public ?string $localDestinoId;
    public ?string $status;
    public ?string $page;
    public ?string $perPage;


    public function __construct(
        string $idGestor,
        ?string $anoReferencia,
        ?string $mesReferencia,
        ?string $codigoPrograma,
        ?string $dataInicio,
        ?string $dataFim,
        ?string $localOrigemId,
        ?string $localDestinoId,
        ?string $status,
        ?string $page,
        ?string $perPage
    ) {
        $this->idGestor = $idGestor;
        $this->anoReferencia  = $anoReferencia;
        $this->mesReferencia  = $mesReferencia;
        $this->codigoPrograma  = $codigoPrograma;
        $this->dataInicio  = $dataInicio;
        $this->dataFim  = $dataFim;
        $this->localOrigemId  = $localOrigemId;
        $this->localDestinoId  = $localDestinoId;
        $this->status  = $status;
        $this->page  = $page;
        $this->perPage  = $perPage;
    }

    public static function fromArray(array $data): ConsultaFaturaInput
    {
        return new self(
            $data['id_gestor'],
            $data['ano_referencia'] ?? null,
            $data['mes_referencia'] ?? null,
            $data['codigo_programa'] ?? null,
            $data['data_inicio'] ?? null,
            $data['data_fim'] ?? null,
            $data['local_origem_id'] ?? null,
            $data['local_destino_id'] ?? null,
            $data['status'] ?? null,
            $data['page'] ?? null,
            $data['per_page'] ?? null
        );
    }

    public function toArray(): array
    {
        return [
            'id_gestor' => $this->idGestor,
            'ano_referencia' => $this->anoReferencia,
            'mes_referencia' => $this->mesReferencia,
            'codigo_programa' => $this->codigoPrograma,
            'data_inicio' => $this->dataInicio,
            'data_fim' => $this->dataFim,
            'local_origem_id' => $this->localOrigemId,
            'local_destino_id' => $this->localDestinoId,
            'status' => $this->status,
            'page' => $this->page,
            'per_page' => $this->perPage
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        $params = [
            'IdGestor' => $this->idGestor,
            'AccessToken' => $accessToken,
        ];

        if ($this->anoReferencia) {
            $params['AnoReferencia'] = $this->anoReferencia;
        }
        if ($this->mesReferencia) {
            $params['MesReferencia'] = $this->mesReferencia;
        }
        if ($this->codigoPrograma) {
            $params['CodigoPrograma'] = $this->codigoPrograma;
        }
        if ($this->dataInicio) {
            $params['DtEmissao'] = $this->dataInicio;
        }
        if ($this->dataFim) {
            $params['DtEmissaoFim'] = $this->dataFim;
        }
        if ($this->localOrigemId) {
            $params['LocalOrigemId'] = $this->localOrigemId;
        }
        if ($this->localDestinoId) {
            $params['LocalDestinoId'] = $this->localDestinoId;
        }
        if ($this->status) {
            $params['Status'] = $this->status;
        }
        if ($this->page) {
            $params['Page'] = $this->page;
        }
        if ($this->perPage) {
            $params['PerPage'] = $this->perPage;
        }

        return $params;
    }
}
