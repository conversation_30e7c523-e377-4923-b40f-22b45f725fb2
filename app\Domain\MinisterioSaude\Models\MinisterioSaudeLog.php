<?php

namespace Domain\MinisterioSaude\Models;

use Illuminate\Database\Eloquent\Model;

class MinisterioSaudeLog extends Model
{
    protected $connection = 'ministerio_saude_sp';
    protected $table = 'ministerio_saude_logs';

    protected $fillable = [
        'operacao',
        'endpoint',
        'metodo_http',
        'request_data',
        'response_data',
        'status_code',
        'request_token',
        'result_code',
        'message',
        'dt_start',
        'dt_end',
        'tempo_resposta_ms',
        'sucesso',
        'erro_message',
        'stack_trace',
        'usuario_id',
        'ip_address'
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'sucesso' => 'boolean',
        'status_code' => 'integer',
        'result_code' => 'integer',
        'tempo_resposta_ms' => 'integer',
        'dt_start' => 'datetime',
        'dt_end' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $dates = [
        'dt_start',
        'dt_end',
        'created_at',
        'updated_at'
    ];

    // Scopes
    public function scopeSuccesso($query)
    {
        return $query->where('sucesso', true);
    }

    public function scopeErro($query)
    {
        return $query->where('sucesso', false);
    }

    public function scopePorOperacao($query, $operacao)
    {
        return $query->where('operacao', $operacao);
    }

    public function scopePorStatusCode($query, $statusCode)
    {
        return $query->where('status_code', $statusCode);
    }

    public function scopeUltimos($query, $dias = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($dias));
    }

    public function scopeHoje($query)
    {
        return $query->whereDate('created_at', today());
    }

    // Accessors
    public function getTempoRespostaSegundosAttribute()
    {
        return $this->tempo_resposta_ms ? round($this->tempo_resposta_ms / 1000, 2) : null;
    }

    public function getDuracaoOperacaoAttribute()
    {
        if ($this->dt_start && $this->dt_end) {
            return $this->dt_start->diffInMilliseconds($this->dt_end);
        }
        return null;
    }

    // Métodos estáticos de conveniência
    public static function criarStatus($requestData, $responseData = null, $sucesso = false, $erro = null)
    {
        return self::create([
            'operacao' => 'criar_status',
            'endpoint' => '/Fatura/inserirStatusOp',
            'metodo_http' => 'POST',
            'request_data' => $requestData,
            'response_data' => $responseData,
            'status_code' => $responseData['status_code'] ?? 0,
            'request_token' => $responseData['RequestToken'] ?? null,
            'result_code' => $responseData['ResultCode'] ?? null,
            'message' => $responseData['Message'] ?? null,
            'dt_start' => $responseData['DtStart'] ?? null,
            'dt_end' => $responseData['DtEnd'] ?? null,
            'sucesso' => $sucesso,
            'erro_message' => $erro,
            'ip_address' => request()->ip()
        ]);
    }

    public static function consultarStatus($requestData, $responseData = null, $sucesso = false, $erro = null)
    {
        return self::create([
            'operacao' => 'consultar_status',
            'endpoint' => '/Fatura/ConsultarStatusOp',
            'metodo_http' => 'GET',
            'request_data' => $requestData,
            'response_data' => $responseData,
            'status_code' => $responseData['status_code'] ?? 0,
            'request_token' => $responseData['RequestToken'] ?? null,
            'result_code' => $responseData['ResultCode'] ?? null,
            'message' => $responseData['Message'] ?? null,
            'dt_start' => $responseData['DtStart'] ?? null,
            'dt_end' => $responseData['DtEnd'] ?? null,
            'sucesso' => $sucesso,
            'erro_message' => $erro,
            'ip_address' => request()->ip()
        ]);
    }

    public static function consultarEndereco($requestData, $responseData = null, $sucesso = false, $erro = null)
    {
        return self::create([
            'operacao' => 'consultar_endereco',
            'endpoint' => '/Fatura/consultarEnderecoLocal',
            'metodo_http' => 'GET',
            'request_data' => $requestData,
            'response_data' => $responseData,
            'status_code' => $responseData['status_code'] ?? 0,
            'request_token' => $responseData['RequestToken'] ?? null,
            'result_code' => $responseData['ResultCode'] ?? null,
            'message' => $responseData['Message'] ?? null,
            'dt_start' => $responseData['DtStart'] ?? null,
            'dt_end' => $responseData['DtEnd'] ?? null,
            'sucesso' => $sucesso,
            'erro_message' => $erro,
            'ip_address' => request()->ip()
        ]);
    }
}
