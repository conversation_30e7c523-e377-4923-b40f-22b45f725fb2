<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePedidosFarmanetTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('pedidos_farmanet', function (Blueprint $table) {
            $table->id();
            
            // Identificadores principais
            $table->string('id_pedido_ms', 50)->unique()->comment('ID do pedido no Ministério da Saúde');
            $table->string('id_gestor', 22)->index()->comment('ID do gestor');
            $table->integer('codigo_programa')->nullable()->comment('Código do programa (5,24,25,28)');
            
            // Dados do pedido
            $table->string('numero_pedido', 100)->nullable()->comment('Número do pedido interno');
            $table->tinyInteger('status_pedido')->default(1)->comment('1-<PERSON><PERSON><PERSON>, 2-<PERSON> Processamento, 3-<PERSON><PERSON><PERSON>, 4-<PERSON><PERSON><PERSON><PERSON>, 5-<PERSON>celado');
            
            // Datas importantes
            $table->datetime('data_pedido')->nullable()->comment('Data do pedido');
            $table->datetime('data_entrega_prevista')->nullable()->comment('Data prevista para entrega');
            $table->datetime('data_ultima_atualizacao')->nullable()->comment('Última atualização');
            
            // Valores
            $table->decimal('valor_total', 15, 2)->nullable()->comment('Valor total do pedido');
            
            // Observações e dados originais
            $table->text('observacoes')->nullable()->comment('Observações gerais');
            $table->longText('dados_originais')->nullable()->comment('JSON com dados originais da API');
            
            // Controle interno
            $table->timestamps();
            
            // Índices
            $table->index(['id_gestor', 'codigo_programa']);
            $table->index(['status_pedido', 'data_pedido']);
            $table->index('data_pedido');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('pedidos_farmanet');
    }
}
