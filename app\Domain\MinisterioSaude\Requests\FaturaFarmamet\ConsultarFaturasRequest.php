<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class ConsultarFaturasRequest extends ApiFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id_gestor' => 'required|string|max:50',
            'ano_referencia' => 'required|integer|min:2000|max:2050',
            'mes_referencia' => 'required|integer|min:1|max:12',
            'codigo_programa' => 'required|integer|min:1',
            'data_inicio' => 'required|date_format:Y-m-d',
            'data_fim' => 'required|date_format:Y-m-d|after_or_equal:data_inicio',
            // Campos opcionais para filtros adicionais
            'local_origem_id' => 'nullable|integer',
            'local_destino_id' => 'nullable|integer',
            'status' => 'nullable|string|max:10',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'id_gestor.required' => 'O ID do gestor é obrigatório',
            'id_gestor.string' => 'O ID do gestor deve ser uma string',
            'id_gestor.max' => 'O ID do gestor não pode ter mais de 50 caracteres',
            'ano_referencia.required' => 'O ano de referência é obrigatório',
            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
            'ano_referencia.min' => 'O ano de referência deve ser no mínimo 2000',
            'ano_referencia.max' => 'O ano de referência deve ser no máximo 2050',
            'mes_referencia.required' => 'O mês de referência é obrigatório',
            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
            'mes_referencia.min' => 'O mês de referência deve ser no mínimo 1',
            'mes_referencia.max' => 'O mês de referência deve ser no máximo 12',
            'codigo_programa.required' => 'O código do programa é obrigatório',
            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
            'codigo_programa.min' => 'O código do programa deve ser maior que 0',
            'data_inicio.date_format' => 'A data de início deve estar no formato Y-m-d',
            'data_inicio.required' => 'A data de início é obrigatória',
            'data_fim.date_format' => 'A data de fim deve estar no formato Y-m-d',
            'data_fim.required' => 'A data de fim é obrigatória',
            'data_fim.after_or_equal' => 'A data de fim deve ser posterior ou igual à data de início',
            'local_origem_id.integer' => 'O ID do local de origem deve ser um número inteiro',
            'local_destino_id.integer' => 'O ID do local de destino deve ser um número inteiro',
            'status.string' => 'O status deve ser uma string',
            'status.max' => 'O status não pode ter mais de 10 caracteres',
            'page.integer' => 'A página deve ser um número inteiro',
            'page.min' => 'A página deve ser no mínimo 1',
            'per_page.integer' => 'O número de itens por página deve ser um número inteiro',
            'per_page.min' => 'O número de itens por página deve ser no mínimo 1',
            'per_page.max' => 'O número de itens por página deve ser no máximo 100'
        ];
    }
}
