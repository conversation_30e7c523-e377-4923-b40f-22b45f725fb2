<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Domain\MinisterioSaude\Requests\RecebimentoFaturasFarmanetRequest;
use Domain\MinisterioSaude\Services\RecebimentoFaturasFarmanetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RecebimentoFaturasFarmanetController extends Controller
{
    protected $recebimentoFaturasService;

    public function __construct(RecebimentoFaturasFarmanetService $recebimentoFaturasService)
    {
        $this->recebimentoFaturasService = $recebimentoFaturasService;
    }

    /**
     * Consulta faturas do Farmanet via API do Ministério da Saúde
     *
     * @param RecebimentoFaturasFarmanetRequest $request
     * @return JsonResponse
     */
    public function consultarFaturas(RecebimentoFaturasFarmanetRequest $request): JsonResponse
    {
        try {
            Log::info('RecebimentoFaturasFarmanetController - Iniciando consulta de faturas', [
                'request_data' => $request->validated()
            ]);

            $result = $this->recebimentoFaturasService->consultarFaturas($request->validated());

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Faturas consultadas com sucesso',
                    'data' => $result['data'],
                    'total_faturas' => $result['total_faturas'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao consultar faturas',
                    'error' => $result['message'], // Usar 'message' em vez de 'error'
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('RecebimentoFaturasFarmanetController - Erro não tratado', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Confirma recebimento de uma fatura
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmarRecebimento(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'numero_fatura' => 'required|string|max:50',
                'id_gestor' => 'required|string|max:22',
                'access_token' => 'required|string|max:40',
                'observacoes' => 'nullable|string|max:1000',
                'data_recebimento' => 'nullable|date_format:Y-m-d H:i:s'
            ]);

            Log::info('RecebimentoFaturasFarmanetController - Confirmando recebimento de fatura', [
                'numero_fatura' => $request->numero_fatura,
                'id_gestor' => $request->id_gestor
            ]);

            $dadosRecebimento = [];
            if ($request->has('observacoes')) {
                $dadosRecebimento['observacoes'] = $request->observacoes;
            }
            if ($request->has('data_recebimento')) {
                $dadosRecebimento['data_recebimento'] = $request->data_recebimento;
            }

            $result = $this->recebimentoFaturasService->confirmarRecebimento(
                $request->numero_fatura,
                $request->id_gestor,
                $request->access_token,
                $dadosRecebimento
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao confirmar recebimento',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('RecebimentoFaturasFarmanetController - Erro ao confirmar recebimento', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Busca faturas armazenadas localmente
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function buscarFaturasLocais(Request $request): JsonResponse
    {
        try {
            Log::info('RecebimentoFaturasFarmanetController - Buscando faturas locais', [
                'filters' => $request->all()
            ]);

            $result = $this->recebimentoFaturasService->buscarFaturasLocais($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Faturas locais consultadas com sucesso',
                'faturas' => $result['faturas'],
                'total' => $result['total'],
                'timestamp' => $result['timestamp']
            ], 200);

        } catch (\Exception $e) {
            Log::error('RecebimentoFaturasFarmanetController - Erro ao buscar faturas locais', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lista os status possíveis para faturas
     *
     * @return JsonResponse
     */
    public function listarStatus(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'status_possiveis' => $this->recebimentoFaturasService->obterStatusPossiveis()
        ], 200);
    }

    /**
     * Retorna informações sobre a API
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'api' => 'API 3.3 - Recebimento Faturas Farmanet',
            'description' => 'Consulta e gerencia faturas farmacêuticas do programa Farmanet junto ao Ministério da Saúde',
            'version' => '1.0.0',
            'endpoints' => [
                'POST /ministerio-saude/faturas-farmanet/consultar' => 'Consultar faturas na API do MS',
                'POST /ministerio-saude/faturas-farmanet/confirmar-recebimento' => 'Confirmar recebimento de fatura',
                'GET /ministerio-saude/faturas-farmanet/locais' => 'Buscar faturas armazenadas localmente',
                'GET /ministerio-saude/faturas-farmanet/status' => 'Listar status possíveis',
                'GET /ministerio-saude/faturas-farmanet/info' => 'Informações sobre a API'
            ],
            'required_parameters' => [
                'id_gestor' => 'string (max 22)',
                'ano_referencia' => 'integer (YYYY)',
                'access_token' => 'string (max 40)'
            ],
            'optional_parameters' => [
                'codigo_programa' => 'integer (5,24,25,28)',
                'mes_referencia' => 'integer (1-12)',
                'numero_fatura' => 'string (max 50)',
                'status_fatura' => 'integer (1-4)',
                'data_inicio' => 'date (Y-m-d)',
                'data_fim' => 'date (Y-m-d)'
            ],
            'programs' => [
                5 => 'Diabetes',
                24 => 'Dose Certa',
                25 => 'Saúde da Mulher',
                28 => 'Arboviroses'
            ],
            'status_codes' => [
                1 => 'Pendente',
                2 => 'Processada',
                3 => 'Paga',
                4 => 'Cancelada'
            ]
        ], 200);
    }
}
