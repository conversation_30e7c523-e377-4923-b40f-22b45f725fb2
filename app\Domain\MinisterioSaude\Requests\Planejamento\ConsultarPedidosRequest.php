<?php

namespace Domain\MinisterioSaude\Requests\Planejamento;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class ConsultarPedidosRequest extends ApiFormRequest
{
    public function rules(): array
    {
        return [
            'codigo_programa' => 'nullable|integer',
            'ano_referencia' => 'nullable|integer|digits:4|min:2000|max:2199',
            'mes_referencia' => 'nullable|integer|min:1|max:12',
            'ano_periodo_referencia' => 'nullable|integer|digits:4|min:2000|max:2199',
            'id_gestor' => 'nullable|integer|max:22'
        ];
    }

    public function messages(): array
    {
        return [
            'codigo_programa.integer' => 'O código do programa deve ser um número inteiro',
            'ano_referencia.integer' => 'O ano de referência deve ser um número inteiro',
            'ano_referencia.digits' => 'O ano de referência deve ter 4 dígitos',
            'ano_referencia.min' => 'O ano de referência deve ser maior que 2000',
            'ano_referencia.max' => 'O ano de referência deve ser menor que 2200',
            'mes_referencia.integer' => 'O mês de referência deve ser um número inteiro',
            'mes_referencia.min' => 'O mês de referência deve ser entre 1 e 12',
            'mes_referencia.max' => 'O mês de referência deve ser entre 1 e 12',
            'ano_periodo_referencia.integer' => 'O ano período de referência deve ser um número inteiro',
            'ano_periodo_referencia.digits' => 'O ano período de referência deve ter 4 dígitos',
            'ano_periodo_referencia.min' => 'O ano período de referência deve ser maior que 2000',
            'ano_periodo_referencia.max' => 'O ano período de referência deve ser menor que 2200',
            'id_gestor.integer' => 'O ID do gestor deve ser um número inteiro',
            'id_gestor.max' => 'O ID do gestor deve ter no máximo 22 caracteres'
        ];
    }

    public function attributes(): array
    {
        return [
            'codigo_programa' => 'Código do Programa',
            'ano_referencia' => 'Ano de Referência',
            'mes_referencia' => 'Mês de Referência',
            'ano_periodo_referencia' => 'Ano do Período de Referência',
            'id_gestor' => 'ID do Gestor'
        ];
    }
}
