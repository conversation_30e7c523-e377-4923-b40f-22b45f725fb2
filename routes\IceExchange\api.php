<?php

use Api\IceExchange\Controllers\CustomerDeadlinesController;
use Api\IceExchange\Controllers\CustomersController;
use Api\IceExchange\Controllers\VolumeEventsController;
use Api\IceExchange\Controllers\IceItemsController;
use Api\IceExchange\Controllers\IceWarehousesController;
use Api\IceExchange\Controllers\VolumesController;
use Api\IceExchange\Controllers\LabelsController;
use Illuminate\Support\Facades\Route;

Route::prefix('ice-exchange')
    ->middleware(['api.jwt'])
    ->group(function () {
        
        // Rotas para clientes
        Route::prefix('customers')->group(function () {
            Route::get('/', [CustomersController::class, 'index']);
            Route::post('/', [CustomersController::class, 'store']);
            Route::get('/{id}', [CustomersController::class, 'show']);
            Route::put('/{id}', [CustomersController::class, 'update']);
            Route::delete('/{id}', [CustomersController::class, 'destroy']);
        });

        // Rotas para prazos de vencimento por cliente
        Route::prefix('customer-deadlines')->group(function () {
            Route::get('/', [CustomerDeadlinesController::class, 'index']);
            Route::post('/', [CustomerDeadlinesController::class, 'store']);
        });

        // Rotas para eventos de volumes
        Route::prefix('volume-events')->group(function () {
            Route::get('/', [VolumeEventsController::class, 'index']);
            Route::post('/', [VolumeEventsController::class, 'store']);
            Route::post('/batch', [VolumeEventsController::class, 'storeBatch']);
            Route::put('/', [VolumeEventsController::class, 'update']);
            Route::delete('/', [VolumeEventsController::class, 'delete']);
        });

        // Rotas para volumes
        Route::prefix('volumes')->group(function () {
            Route::get('/', [VolumesController::class, 'index']);
            Route::get('/{volumeCode}', [VolumesController::class, 'show']);
        });

        // Rotas para tipos de gelo
        Route::prefix('ice-items')->group(function () {
            Route::get('/', [IceItemsController::class, 'index']);
        });

        // Rotas para armazéns/câmaras frias
        Route::prefix('ice-warehouses')->group(function () {
            Route::get('/', [IceWarehousesController::class, 'index']);
        });

        // Rotas para etiquetas
        Route::prefix('labels')->group(function () {
            Route::post('/search-volumes', [LabelsController::class, 'searchVolumes']);
            Route::post('/generate', [LabelsController::class, 'generateLabels']);
        });
    }); 