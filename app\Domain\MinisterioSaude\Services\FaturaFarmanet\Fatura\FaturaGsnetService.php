<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura;

use App\Models\FaturaGsnet;
use App\Models\FaturaGsnetItem;
use Domain\MinisterioSaude\Exceptions\CustomMessageException;
use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\AtualizarStatusFaturaInput;
use Domain\MinisterioSaude\Traits\HasLog;
use Domain\MinisterioSaude\Traits\HasServiceResponse;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ConsultaFaturaInput;
use Domain\MinisterioSaude\Services\FaturaFarmanet\Fatura\Input\ReceberFaturasInput;

class FaturaGsnetService
{
    use HasServiceResponse;
    use HasLog;

    private Client $client;
    private string $baseUrl;
    private string $accessToken;
    private MinisterioSaudeLogRepositoryInterface $logRepository;

    public function __construct(
        MinisterioSaudeLogRepositoryInterface $logRepository
    ) {
        $this->client = new Client([
            'timeout' => config('ministerio_saude.api.timeout', 30),
            'verify' => false,
        ]);

        $environment = config('ministerio_saude.api.environment', 'homolog');
        $this->baseUrl = config("ministerio_saude.api.base_url.{$environment}");
        $this->accessToken = config('ministerio_saude.api.access_token');
        $this->logRepository = $logRepository;
    }
    /**
     * API 2.1 - Consultar Faturas via API real do Ministério
     *
     * @param ConsultaFaturaInput $data
     * @return array
     */
    public function consultarFaturas(ConsultaFaturaInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.consultar_faturas_gsnet');
            $queryParams = $input->toQueryParams($this->accessToken);
            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);

            $this->logInfo('FaturaGsnetService - Consultando faturas via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null,
                'total_faturas' => count($responseBody['Data'] ?? [])
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na consulta', $responseBody);
            }

            $this->logRepository->storeConsultarFaturas(
                $input->toArray(),
                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
                true
            );

            return $this->successResponse($responseBody, 'Consulta de faturas realizada com sucesso via API real do Ministério.');
        } catch (CustomMessageException $e) {

            $this->logRepository->storeConsultarFaturas(
                $input->toArray(),
                $e->getData(),
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";

            $this->logError('FaturaGsnetService - Erro na requisição para o Ministério', $e, [
                'status_code' => $statusCode,
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
                'request_data' => $input->toArray()
            ]);

            $this->logRepository->storeConsultarFaturas(
                $input->toArray(),
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (\Exception $e) {
            $this->logError('FaturaGsnetService - Erro geral', $e, [
                'request_data' => $input->toArray()
            ]);

            $this->logRepository->storeConsultarFaturas(
                $input->toArray(),
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse('Erro interno: ' . $e->getMessage())->setNullData();
        }
        return $response;
    }

    /**
     * API 2.2 - Atualizar Status da Fatura
     *
     * @param AtualizarStatusFaturaInput $input
     * @return ServiceResponse
     */
    public function atualizarStatusFatura(AtualizarStatusFaturaInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.atualizar_status_fatura_gsnet');
            $queryParams = $input->toQueryParams($this->accessToken);
            $url = "{$this->baseUrl}{$endpoint}?" . http_build_query($queryParams);
            $this->logInfo('FaturaGsnetService - Atualizando status da fatura via API real', [
                'url' => $url,
                'params' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);
            $response = $this->client->get($url, [
                'headers' => [
                    'Accept' => 'application/json',
                ]
            ]);
            $responseBody = json_decode($response->getBody()->getContents(), true);
            $this->logInfo('FaturaGsnetService - Resposta da API real recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null
            ]);
            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro na atualização', $responseBody);
            }
            $this->logRepository->storeAtualizarStatusFatura(
                $input->toArray(),
                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
                true
            );
            return $this->successResponse($responseBody, 'Status da fatura atualizado com sucesso');
        } catch (CustomMessageException $e) {
            $this->logRepository->storeAtualizarStatusFatura(
                $input->toArray(),
                $e->getData(),
                false,
                $e->getMessage()
            );
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
            $this->logError('FaturaGsnetService - Erro na atualização do status da fatura', $e, [
                'url' => $url ?? 'URL não definida',
                'data' => $input->toArray() ?? []
            ]);

            $this->logRepository->storeAtualizarStatusFatura(
                $input->toArray(),
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (\Exception $e) {
            $this->logError('FaturaGsnetService - Erro ao atualizar status da fatura', $e);

            $this->logRepository->storeAtualizarStatusFatura(
                $input->toArray(),
                null,
                false,
                $e->getMessage()
            );

            $response =  $this->failureResponse('Erro ao atualizar status da fatura: ' . $e->getMessage())->setNullData();
        }
        return $response;
    }

    /**
     * API 3.3 - Receber Faturas Farmanet (Envio)
     *
     * @param ReceberFaturasInput $input
     * @return ServiceResponse
     */
    public function receberFaturas(ReceberFaturasInput $input): ServiceResponse
    {
        try {
            $endpoint = config('ministerio_saude.farmanet.endpoints.receber_faturas');
            $url = "{$this->baseUrl}{$endpoint}";

            $queryParams = $input->toQueryParams($this->accessToken);
            $this->logInfo('FaturaGsnetService - Enviando fatura para recebimento via API', [
                'url' => $url,
                'payload' => collect($queryParams)->except(['AccessToken'])->toArray()
            ]);

            $response = $this->client->put($url, [
                'json' => $queryParams,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ]
            ]);

            $responseBody = json_decode($response->getBody()->getContents(), true);

            $this->logInfo('FaturaGsnetService - Resposta do recebimento de fatura recebida', [
                'status_code' => $response->getStatusCode(),
                'result_code' => $responseBody['ResultCode'] ?? null,
                'message' => $responseBody['Message'] ?? null,
                'data' => $responseBody['Data'] ?? null
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new CustomMessageException($responseBody['Message'] ?? 'Erro no recebimento da fatura', $responseBody);
            }

            $this->logRepository->storeReceberFaturasFarmanet(
                $queryParams,
                array_merge($responseBody, ['status_code' => $response->getStatusCode()]),
                true
            );

            return $this->successResponse($responseBody, 'Fatura enviada para recebimento com sucesso');
        } catch (CustomMessageException $e) {
            $this->logRepository->storeReceberFaturasFarmanet(
                $queryParams,
                $e->getData(),
                false,
                $e->getMessage()
            );
            $response = $this->failureResponse($e->getMessage())->setData($e->getData());
        } catch (RequestException $e) {
            $errorMessage = "Erro na comunicação com API do Ministério: {$e->getMessage()}";
            $this->logError('FaturaGsnetService@receberFaturas - Erro no recebimento da fatura', $e, [
                'url' => $url ?? 'URL não definida',
                'data' => $queryParams ?? []
            ]);

            $this->logRepository->storeReceberFaturasFarmanet(
                $queryParams,
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse($errorMessage)->setNullData();
        } catch (\Exception $e) {
            $this->logError('FaturaGsnetService@receberFaturas - Erro interno no recebimento de faturas', $e);

            $this->logRepository->storeReceberFaturasFarmanet(
                $queryParams,
                null,
                false,
                $e->getMessage()
            );

            $response = $this->failureResponse('Erro interno: ' . $e->getMessage())->setNullData();
        }
        return $response;
    }

    /**
     * Buscar fatura por protocolo
     *
     * @param string $protocoloId
     * @return FaturaGsnet|null
     */
    public function buscarPorProtocolo(string $protocoloId): ?FaturaGsnet
    {
        return FaturaGsnet::with(['itens', 'statusControle'])
            ->where('protocolo_id_gsnet', $protocoloId)
            ->where('ativo', true)
            ->first();
    }

    /**
     * Criar nova fatura
     *
     * @param array $dadosFatura
     * @param array $itens
     * @return FaturaGsnet
     */
    public function criarFatura(array $dadosFatura, array $itens = []): FaturaGsnet
    {
        try {
            DB::connection('ministerio_saude_sp')->beginTransaction();

            $fatura = FaturaGsnet::create($dadosFatura);

            // Criar itens se fornecidos
            foreach ($itens as $item) {
                $item['fatura_id'] = $fatura->id;
                $item['protocolo_id_gsnet'] = $fatura->protocolo_id_gsnet;
                FaturaGsnetItem::create($item);
            }

            DB::connection('ministerio_saude_sp')->commit();

            return $fatura->load(['itens', 'statusControle']);

        } catch (\Exception $e) {
            DB::connection('ministerio_saude_sp')->rollBack();
            throw $e;
        }
    }

}
