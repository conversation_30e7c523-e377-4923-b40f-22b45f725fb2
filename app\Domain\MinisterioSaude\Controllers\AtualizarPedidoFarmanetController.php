<?php

namespace Domain\MinisterioSaude\Controllers;

use App\Controllers\Controller;
use Domain\MinisterioSaude\Requests\AtualizarPedidoFarmanetRequest;
use Domain\MinisterioSaude\Services\AtualizarPedidoFarmanetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AtualizarPedidoFarmanetController extends Controller
{
    protected $atualizarPedidoService;

    public function __construct(AtualizarPedidoFarmanetService $atualizarPedidoService)
    {
        $this->atualizarPedidoService = $atualizarPedidoService;
    }

    /**
     * Atualiza status de um pedido Farmanet via API do Ministério da Saúde
     *
     * @param AtualizarPedidoFarmanetRequest $request
     * @return JsonResponse
     */
    public function atualizarPedido(AtualizarPedidoFarmanetRequest $request): JsonResponse
    {
        try {
            Log::info('AtualizarPedidoFarmanetController - Iniciando atualização de pedido', [
                'request_data' => $request->validated()
            ]);

            // Validar se a atualização é permitida
            $validacao = $this->atualizarPedidoService->validarAtualizacaoPedido(
                $request->id_pedido_ms,
                $request->id_gestor,
                $request->status_pedido
            );

            if (!$validacao['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Atualização não permitida',
                    'error' => $validacao['message']
                ], 422);
            }

            $result = $this->atualizarPedidoService->atualizarPedido($request->validated());

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data'],
                    'timestamp' => $result['timestamp']
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Erro ao atualizar pedido',
                    'error' => $result['error'],
                    'timestamp' => $result['timestamp']
                ], 422);
            }

        } catch (\Exception $e) {
            Log::error('AtualizarPedidoFarmanetController - Erro não tratado', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Valida se um pedido pode ser atualizado
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validarAtualizacao(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'id_pedido_ms' => 'required|string|max:50',
                'id_gestor' => 'required|string|max:22',
                'novo_status' => 'required|integer|in:1,2,3,4,5'
            ]);

            Log::info('AtualizarPedidoFarmanetController - Validando atualização', [
                'id_pedido_ms' => $request->id_pedido_ms,
                'id_gestor' => $request->id_gestor,
                'novo_status' => $request->novo_status
            ]);

            $validacao = $this->atualizarPedidoService->validarAtualizacaoPedido(
                $request->id_pedido_ms,
                $request->id_gestor,
                $request->novo_status
            );

            return response()->json([
                'success' => true,
                'valid' => $validacao['valid'],
                'message' => $validacao['message'],
                'pedido' => $validacao['pedido'] ?? null
            ], 200);

        } catch (\Exception $e) {
            Log::error('AtualizarPedidoFarmanetController - Erro ao validar atualização', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro interno do servidor',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lista os status possíveis para pedidos
     *
     * @return JsonResponse
     */
    public function listarStatus(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'status_possiveis' => $this->atualizarPedidoService->obterStatusPossiveis(),
            'regras_transicao' => [
                '1 (Pendente)' => 'Pode ir para: Em Processamento, Aprovado, Rejeitado, Cancelado',
                '2 (Em Processamento)' => 'Pode ir para: Aprovado, Rejeitado, Cancelado',
                '3 (Aprovado)' => 'Pode ir para: Cancelado',
                '4 (Rejeitado)' => 'Status final - não pode ser alterado',
                '5 (Cancelado)' => 'Status final - não pode ser alterado'
            ]
        ], 200);
    }

    /**
     * Retorna informações sobre a API
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'api' => 'API 3.2 - Atualizar Pedidos Farmanet',
            'description' => 'Atualiza status de pedidos farmacêuticos do programa Farmanet junto ao Ministério da Saúde',
            'version' => '1.0.0',
            'endpoints' => [
                'PUT /ministerio-saude/pedidos-farmanet/atualizar' => 'Atualizar status de pedido',
                'POST /ministerio-saude/pedidos-farmanet/validar-atualizacao' => 'Validar se pedido pode ser atualizado',
                'GET /ministerio-saude/pedidos-farmanet/status' => 'Listar status possíveis',
                'GET /ministerio-saude/pedidos-farmanet/atualizar/info' => 'Informações sobre a API'
            ],
            'required_parameters' => [
                'id_gestor' => 'string (max 22)',
                'id_pedido_ms' => 'string (max 50)',
                'status_pedido' => 'integer (1-5)',
                'data_atualizacao' => 'datetime (Y-m-d H:i:s)',
                'access_token' => 'string (max 40)'
            ],
            'optional_parameters' => [
                'observacoes' => 'string (max 1000)'
            ],
            'status_codes' => [
                1 => 'Pendente',
                2 => 'Em Processamento',
                3 => 'Aprovado',
                4 => 'Rejeitado',
                5 => 'Cancelado'
            ]
        ], 200);
    }
}
