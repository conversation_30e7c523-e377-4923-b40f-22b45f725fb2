<?php

namespace Domain\MinisterioSaude\Helpers;

use Throwable;

class ServiceResponse
{
    public $data = null;
    public bool $success = true;
    public string $message = '';
    public ?string $errorCode = null;
    public array $errors = [];

    public function __construct($data = null)
    {
        if ($data !== null) {
            $this->data = $data;
        }
    }

    public function setSuccess(bool $success = true): self
    {
        $this->success = $success;
        return $this;
    }

    public function setFailure(string $message, $data = null): self
    {
        $this->success = false;
        $this->message = $message;

        if ($data !== null) {
            $this->data = $data;
        }

        return $this;
    }

    public function setFailureFromException(Throwable $exception): self
    {
        $this->success = false;
        $this->message = $exception->getMessage();
        return $this;
    }

    public function setData($data): self
    {
        $this->data = $data;
        return $this;
    }

    public function setNullData(): self
    {
        $this->data = null;
        return $this;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function setErrorCode(string $code): self
    {
        $this->errorCode = $code;
        return $this;
    }

    public function addError(string $field, string $message): self
    {
        $this->errors[$field][] = $message;
        return $this;
    }

    public function setErrors(array $errors): self
    {
        $this->errors = $errors;
        return $this;
    }

    public function clearErrors(): self
    {
        $this->errors = [];
        return $this;
    }

    public function addErrors(array $errors): self
    {
        foreach ($errors as $field => $messages) {
            if (is_array($messages)) {
                foreach ($messages as $message) {
                    $this->addError($field, $message);
                }
            } else {
                $this->addError($field, $messages);
            }
        }
        return $this;
    }

    public static function success($data = null, string $message = ''): self
    {
        $response = new self($data);
        $response->message = $message;
        return $response;
    }

    public static function failure(string $message, $data = null): self
    {
        return (new self())->setFailure($message, $data);
    }

    public static function fromException(Throwable $exception, $data = null): self
    {
        return (new self($data))->setFailureFromException($exception);
    }

    public static function validationError(array $errors, string $message = 'Dados inválidos'): self
    {
        return (new self())
            ->setFailure($message)
            ->setErrors($errors)
            ->setErrorCode('VALIDATION_ERROR');
    }

    public static function notFound(string $message = 'Recurso não encontrado'): self
    {
        return (new self())
            ->setFailure($message)
            ->setErrorCode('NOT_FOUND');
    }

    public static function unauthorized(string $message = 'Não autorizado'): self
    {
        return (new self())
            ->setFailure($message)
            ->setErrorCode('UNAUTHORIZED');
    }

    public static function forbidden(string $message = 'Acesso negado'): self
    {
        return (new self())
            ->setFailure($message)
            ->setErrorCode('FORBIDDEN');
    }

    public static function internalError(string $message = 'Erro interno do servidor'): self
    {
        return (new self())
            ->setFailure($message)
            ->setErrorCode('INTERNAL_ERROR');
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function isFailure(): bool
    {
        return !$this->success;
    }

    public function hasData(): bool
    {
        return $this->data !== null;
    }

    public function hasMessage(): bool
    {
        return !empty($this->message);
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function hasErrorCode(): bool
    {
        return $this->errorCode !== null;
    }

    public function hasError(string $field): bool
    {
        return isset($this->errors[$field]);
    }

    public function getData()
    {
        return $this->data;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getErrorsForField(string $field): array
    {
        return $this->errors[$field] ?? [];
    }

    public function getFirstError(string $field = null): ?string
    {
        if ($field) {
            return $this->errors[$field][0] ?? null;
        }

        foreach ($this->errors as $fieldErrors) {
            return $fieldErrors[0] ?? null;
        }

        return null;
    }

    public function toArray(): array
    {
        $result = [
            'success' => $this->success,
            'message' => $this->message,
        ];

        if ($this->data !== null) {
            $result['data'] = $this->data;
        }

        if ($this->errorCode) {
            $result['error_code'] = $this->errorCode;
        }

        if (!empty($this->errors)) {
            $result['errors'] = $this->errors;
        }

        return $result;
    }

    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }

    public function toResponse(int $statusCode = null): \Illuminate\Http\JsonResponse
    {
        $statusCode ??= $this->getDefaultStatusCode();
        return response()->json($this->toArray(), $statusCode);
    }

    public function toSuccessResponse(): \Illuminate\Http\JsonResponse
    {
        return $this->toResponse(200);
    }

    public function toErrorResponse(int $statusCode = null): \Illuminate\Http\JsonResponse
    {
        $statusCode ??= $this->getDefaultErrorStatusCode();
        return $this->toResponse($statusCode);
    }

    private function getDefaultStatusCode(): int
    {
        if ($this->success) {
            return 200;
        }

        return $this->getDefaultErrorStatusCode();
    }

    private function getDefaultErrorStatusCode(): int
    {
        switch ($this->errorCode) {
            case 'BAD_REQUEST':
                $statusCode = 400;
                break;
            case 'UNAUTHORIZED':
                $statusCode = 401;
                break;
            case 'FORBIDDEN':
                $statusCode = 403;
                break;
            case 'NOT_FOUND':
                $statusCode = 404;
                break;
            case 'METHOD_NOT_ALLOWED':
                $statusCode = 405;
                break;
            case 'REQUEST_TIMEOUT':
                $statusCode = 408;
                break;
            case 'REQUEST_ENTITY_TOO_LARGE':
                $statusCode = 413;
                break;
            case 'CONFLICT':
                $statusCode = 409;
                break;
            case 'VALIDATION_ERROR':
                $statusCode = 422;
                break;
            case 'TOO_MANY_REQUESTS':
                $statusCode = 429;
                break;
            case 'INTERNAL_ERROR':
                $statusCode = 500;
                break;
            case 'SERVICE_UNAVAILABLE':
                $statusCode = 503;
                break;
            case 'GATEWAY_TIMEOUT':
                $statusCode = 504;
                break;
            default:
                $statusCode = 400;
        }

        return $statusCode;
    }

    public function setValidationErrors(\Illuminate\Support\MessageBag $messageBag): self
    {
        $this->errors = $messageBag->toArray();
        $this->setFailure('Dados inválidos')
             ->setErrorCode('VALIDATION_ERROR');
        return $this;
    }

    public function setValidatorErrors(\Illuminate\Validation\Validator $validator): self
    {
        return $this->setValidationErrors($validator->errors());
    }

    public function when(bool $condition, callable $callback): self
    {
        if ($condition) {
            $callback($this);
        }
        return $this;
    }

    public function unless(bool $condition, callable $callback): self
    {
        return $this->when(!$condition, $callback);
    }

    public function tap(callable $callback): self
    {
        $callback($this);
        return $this;
    }

    public function __toString(): string
    {
        return $this->toJson();
    }
}
