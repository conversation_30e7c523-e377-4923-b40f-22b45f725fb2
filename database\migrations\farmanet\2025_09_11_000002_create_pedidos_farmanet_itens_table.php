<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePedidosFarmanetItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('pedidos_farmanet_itens', function (Blueprint $table) {
            $table->id();
            
            // Relacionamento com pedido
            $table->foreignId('pedido_farmanet_id')->constrained('pedidos_farmanet')->onDelete('cascade');
            
            // Identificação do medicamento
            $table->string('codigo_medicamento', 20)->nullable()->comment('Código do medicamento');
            $table->string('nome_medicamento', 255)->nullable()->comment('Nome do medicamento');
            $table->string('codigo_farmaco', 20)->nullable()->comment('Código do fármaco');
            
            // Quantidades
            $table->integer('quantidade_solicitada')->default(0)->comment('Quantidade solicitada');
            $table->integer('quantidade_aprovada')->nullable()->comment('Quantidade aprovada');
            $table->integer('quantidade_entregue')->nullable()->comment('Quantidade entregue');
            
            // Valores
            $table->decimal('valor_unitario', 10, 4)->nullable()->comment('Valor unitário');
            $table->decimal('valor_total', 15, 2)->nullable()->comment('Valor total do item');
            
            // Status do item
            $table->tinyInteger('status_item')->default(1)->comment('1-Pendente, 2-Aprovado, 3-Rejeitado, 4-Entregue');
            
            // Observações e dados originais
            $table->text('observacoes')->nullable()->comment('Observações do item');
            $table->longText('dados_originais')->nullable()->comment('JSON com dados originais da API');
            
            // Controle interno
            $table->timestamps();
            
            // Índices
            $table->index(['codigo_medicamento', 'codigo_farmaco']);
            $table->index('status_item');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('pedidos_farmanet_itens');
    }
}
