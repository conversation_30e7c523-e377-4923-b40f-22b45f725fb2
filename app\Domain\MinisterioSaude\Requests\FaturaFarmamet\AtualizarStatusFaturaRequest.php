<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class AtualizarStatusFaturaRequest extends ApiFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'protocolo_id_gsnet' => 'required|integer|max:16',
            'nr_documento' => 'required|integer|max:100',
            'id_origem' => 'required|string|min:1|max:16',
            'justificativa' => 'nullable|string|max:255'
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'protocolo_id_gsnet.required' => 'O protocolo ID GSNET é obrigatório',
            'protocolo_id_gsnet.integer' => 'O protocolo ID GSNET deve ser um número inteiro',
            'protocolo_id_gsnet.max' => 'O protocolo ID GSNET deve ter no máximo 16 caracteres',
            'nr_documento.required' => 'O número do documento é obrigatório',
            'nr_documento.integer' => 'O número do documento deve ser uma  número inteiro',
            'nr_documento.max' => 'O número do documento deve ter no máximo 100 caracteres',
            'id_origem.required' => 'O ID de origem é obrigatório',
            'id_origem.string' => 'O ID de origem deve ser uma string',
            'id_origem.max' => 'O ID de origem deve ser menor ou igual a 16',
            'id_origem.min' => 'O ID de origem deve ser maior ou igual a 1',
            'justificativa.string' => 'A justificativa deve ser uma string',
            'justificativa.max' => 'A justificativa deve ter no máximo 255 caracteres'
        ];
    }
}
