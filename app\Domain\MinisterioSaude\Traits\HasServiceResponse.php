<?php

namespace Domain\MinisterioSaude\Traits;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Throwable;

trait HasServiceResponse
{
    protected function successResponse($data = null, string $message = ''): ServiceResponse
    {
        return ServiceResponse::success($data, $message);
    }

    protected function failureResponse(string $message, $data = null): ServiceResponse
    {
        return ServiceResponse::failure($message, $data);
    }

    protected function exceptionResponse(Throwable $exception, $data = null): ServiceResponse
    {
        return ServiceResponse::fromException($exception, $data);
    }

    protected function validationResponse(array $errors, string $message = 'Dados inválidos'): ServiceResponse
    {
        return ServiceResponse::validationError($errors, $message);
    }

    protected function notFoundResponse(string $resource = 'Recurso'): ServiceResponse
    {
        return ServiceResponse::notFound($resource);
    }

    protected function unauthorizedResponse(string $message = 'Não autorizado'): ServiceResponse
    {
        return ServiceResponse::unauthorized($message);
    }

    protected function forbiddenResponse(string $message = 'Acesso negado'): ServiceResponse
    {
        return ServiceResponse::forbidden($message);
    }

    protected function internalErrorResponse(string $message = 'Erro interno do servidor'): ServiceResponse
    {
        return ServiceResponse::internalError($message);
    }
}
