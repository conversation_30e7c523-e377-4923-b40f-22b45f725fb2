<?php

namespace Domain\MinisterioSaude\Requests\FaturaFarmamet;

use Domain\MinisterioSaude\Requests\Common\ApiFormRequest;

class ConsultarEnderecoRequest extends ApiFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'IdGestor' => 'required_without:id_gestor|string|max:4',
            'id_gestor' => 'required_without:IdGestor|string|max:4',
            'IdLocal' => 'required_without:id_local|string|max:5',
            'id_local' => 'required_without:IdLocal|string|max:5',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Normalizar parâmetros: usar versão PascalCase se disponível
        $data = $this->all();

        if (isset($data['id_gestor']) && !isset($data['IdGestor'])) {
            $this->merge(['IdGestor' => $data['id_gestor']]);
        }

        if (isset($data['id_local']) && !isset($data['IdLocal'])) {
            $this->merge(['IdLocal' => $data['id_local']]);
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'IdGestor.required_without' => 'IdGestor ou id_gestor é obrigatório',
            'IdGestor.string' => 'IdGestor deve ser uma string',
            'IdGestor.max' => 'IdGestor deve ter no máximo 4 caracteres',
            'id_gestor.required_without' => 'id_gestor ou IdGestor é obrigatório',
            'id_gestor.string' => 'id_gestor deve ser uma string',
            'id_gestor.max' => 'id_gestor deve ter no máximo 4 caracteres',
            'IdLocal.required_without' => 'IdLocal ou id_local é obrigatório',
            'IdLocal.string' => 'IdLocal deve ser uma string',
            'IdLocal.max' => 'IdLocal deve ter no máximo 5 caracteres',
            'id_local.required_without' => 'id_local ou IdLocal é obrigatório',
            'id_local.string' => 'id_local deve ser uma string',
            'id_local.max' => 'id_local deve ter no máximo 5 caracteres',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'IdGestor' => 'ID do Gestor',
            'IdLocal' => 'ID do Local'
        ];
    }
}
