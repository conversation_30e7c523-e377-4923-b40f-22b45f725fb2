<?php

namespace Domain\IceExchange\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Domain\NOTFIS\Models\NotfisVolume;

class VolumeEvent extends Model
{
    protected $connection = null; // usar conexão padrão
    protected $table = 'ice_change_volume_events';

    protected $fillable = [
        'notfis_volume_id',
        'event_type',
        'event_date',
        'due_date',
        'user_id',
        'ice_type_id',
        'warehouse_id',
        'deadline_hours',
        'emit_label',
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'due_date' => 'datetime',
        'deadline_hours' => 'integer',
        'emit_label' => 'boolean',
    ];

    public function notfisVolume(): BelongsTo
    {
        return $this->belongsTo(NotfisVolume::class, 'notfis_volume_id');
    }

    public function user(): BelongsTo
    {
        return $this->setConnection('users_service')->belongsTo(\App\Models\User::class);
    }

    public function iceType(): BelongsTo
    {
        return $this->belongsTo(IceItem::class, 'ice_type_id');
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(IceWarehouse::class, 'warehouse_id');
    }

    public function audit(): HasMany
    {
        return $this->hasMany(VolumeEventAudit::class, 'volume_event_id');
    }

    // Métodos auxiliares para acessar dados do volume e cliente
    public function getVolumeCode()
    {
        return $this->notfisVolume?->volume_code;
    }

    public function getInvoiceKey()
    {
        return $this->notfisVolume?->notfis?->invoice_key;
    }

    public function getCustomerId()
    {
        return $this->notfisVolume?->notfis?->sender_customer_id;
    }

    public function getCustomer()
    {
        $customerId = $this->getCustomerId();
        if ($customerId) {
            return \Domain\Customers\Models\Customer::find($customerId);
        }
        return null;
    }
} 