<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogsMinisterioSaudeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('logs_ministerio_saude', function (Blueprint $table) {
            $table->id();
            
            // Identificação da operação
            $table->string('api_endpoint', 255)->comment('Endpoint da API chamado');
            $table->string('metodo_http', 10)->comment('Método HTTP (GET, POST, PUT, etc)');
            $table->string('operacao', 100)->comment('Nome da operação realizada');
            
            // Dados da requisição
            $table->longText('parametros_enviados')->nullable()->comment('JSON com parâmetros enviados');
            $table->text('headers_requisicao')->nullable()->comment('Headers da requisição');
            
            // Dados da resposta
            $table->integer('status_code')->comment('Status code da resposta HTTP');
            $table->longText('resposta_api')->nullable()->comment('Resposta completa da API');
            $table->text('headers_resposta')->nullable()->comment('Headers da resposta');
            
            // Controle de timing
            $table->decimal('tempo_resposta', 8, 3)->nullable()->comment('Tempo de resposta em segundos');
            $table->datetime('data_inicio')->comment('Data/hora de início da requisição');
            $table->datetime('data_fim')->nullable()->comment('Data/hora de fim da requisição');
            
            // Identificação do usuário/sistema
            $table->string('usuario_solicitante', 100)->nullable()->comment('Usuário que fez a requisição');
            $table->string('ip_origem', 45)->nullable()->comment('IP de origem da requisição');
            $table->string('user_agent', 500)->nullable()->comment('User Agent');
            
            // Controle de sucesso/erro
            $table->boolean('sucesso')->default(true)->comment('Indica se a operação foi bem-sucedida');
            $table->text('mensagem_erro')->nullable()->comment('Mensagem de erro, se houver');
            $table->text('stack_trace')->nullable()->comment('Stack trace em caso de erro');
            
            // Dados adicionais para auditoria
            $table->string('session_id', 255)->nullable()->comment('ID da sessão');
            $table->string('request_id', 100)->nullable()->comment('ID único da requisição');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices para performance
            $table->index(['api_endpoint', 'data_inicio']);
            $table->index(['sucesso', 'data_inicio']);
            $table->index(['operacao', 'data_inicio']);
            $table->index('status_code');
            $table->index('data_inicio');
            $table->index('usuario_solicitante');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('logs_ministerio_saude');
    }
}
