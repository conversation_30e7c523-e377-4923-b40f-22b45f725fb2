<?php

namespace Domain\MinisterioSaude\Controllers;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Domain\MinisterioSaude\Traits\HasLog;
use Illuminate\Http\JsonResponse;
use App\Controllers\Controller;
use Illuminate\Support\Facades\Log;
// Requests
use Domain\MinisterioSaude\Requests\Planejamento\ConsultarPedidosRequest;
use Domain\MinisterioSaude\Requests\Planejamento\AtualizarPedidoRequest;
// Inputs
use Domain\MinisterioSaude\Services\Planejamento\Input\AtualizarPedidoInput;
use Domain\MinisterioSaude\Services\Planejamento\Input\ConsultarPedidosInput;
// Services
use Domain\MinisterioSaude\Services\Planejamento\PlanejamentoService;

class PlanejamentoController extends Controller
{
    use HasLog;

    private PlanejamentoService $planejamentoService;

    public function __construct(PlanejamentoService $planejamentoService)
    {
        $this->planejamentoService = $planejamentoService;
    }

    /**
     * API 3.1 Consultar Pedidos Farmanet
     *
     * Serviço utilizado para consultar as faturas do GSNET.
     *
     * @param ConsultarPedidosRequest $request
     * @return JsonResponse
     */
    public function consultarPedidos(ConsultarPedidosRequest $request): JsonResponse
    {
        try {
            $result = $this->planejamentoService->consultarPedidos(ConsultarPedidosInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('ItemController@consultarItens - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }

    /**
     * API 3.2 Atualizar Pedidos Farmanet
     *
     * Serviço utilizado para atualizar os pedidos do GSNET.
     *
     * @param AtualizarPedidoRequest $request
     * @return JsonResponse
     */
    public function atualizarPedido(AtualizarPedidoRequest $request): JsonResponse
    {
        try {
            $result = $this->planejamentoService->atualizarPedido(AtualizarPedidoInput::fromArray($request->validated()));
            return $result->toResponse();
        } catch (\Exception $e) {
            $this->logError('ItemController@consultarItens - Erro', $e);
            return ServiceResponse::internalError('Erro interno: ' . $e->getMessage())->toResponse();
        }
    }
}
