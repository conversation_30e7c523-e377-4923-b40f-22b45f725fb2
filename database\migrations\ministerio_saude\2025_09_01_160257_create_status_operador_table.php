<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStatusOperadorTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('status_operador', function (Blueprint $table) {
            $table->id();
            
            // Dados principais do status
            $table->string('codigo_operador', 50)->comment('Código do operador');
            $table->string('nome_operador', 255)->comment('Nome do operador');
            $table->tinyInteger('status')->default(1)->comment('Status do operador (1-Ativo, 0-Inativo)');
            
            // Informações adicionais
            $table->string('cnpj', 18)->nullable()->comment('CNPJ do operador');
            $table->string('inscricao_estadual', 20)->nullable()->comment('Inscrição estadual');
            $table->text('observacoes')->nullable()->comment('Observações do status');
            
            // Dados da API
            $table->longText('dados_originais')->nullable()->comment('Dados originais da resposta da API');
            
            // Controle de datas
            $table->datetime('data_consulta')->nullable()->comment('Data da consulta na API');
            $table->datetime('data_ultima_atualizacao')->nullable()->comment('Última atualização dos dados');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices
            $table->unique('codigo_operador');
            $table->index(['status', 'data_consulta']);
            $table->index('cnpj');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('status_operador');
    }
}
