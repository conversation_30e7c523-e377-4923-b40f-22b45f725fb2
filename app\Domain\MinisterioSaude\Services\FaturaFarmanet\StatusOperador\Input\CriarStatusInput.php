<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;

use Illuminate\Http\Request;

class CriarStatusInput
{
    public string $idOrigem;
    public string $nomeStatus;
    public string $descricaoStatus;
    public ?string $systemCode = null;

    public function __construct(string $idOrigem, string $nomeStatus, string $descricaoStatus, ?string $systemCode = null)
    {
        $this->idOrigem = $idOrigem;
        $this->nomeStatus = $nomeStatus;
        $this->descricaoStatus = $descricaoStatus;
        $this->systemCode = $systemCode;
    }

    public function toArray(): array
    {
        return [
            'id_origem' => $this->idOrigem,
            'nome_status' => $this->nomeStatus,
            'descricao_status' => $this->descricaoStatus,
            'system_code' => $this->systemCode
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        return [
            'Data' => [
                'IdOrigem' => $this->idOrigem,
                'NomeStatus' => $this->nomeStatus,
                'DescricaoStatus' => $this->descricaoStatus
            ],
            'AccessToken' => $accessToken,
            'SystemCode' => $this->systemCode ?? config('ministerio_saude.api.system_code', 'IBL_LOGISTICS')
        ];
    }

    public static function fromRequest(Request $request): CriarStatusInput
    {
        return new self(
            $request->input('id_origem'),
            $request->input('nome_status'),
            $request->input('descricao_status'),
            $request->input('system_code')
        );
    }

    public static function fromArray(array $data): CriarStatusInput
    {
        return new self(
            $data['id_origem'],
            $data['nome_status'],
            $data['descricao_status'],
            $data['system_code'] ?? null
        );
    }
}
