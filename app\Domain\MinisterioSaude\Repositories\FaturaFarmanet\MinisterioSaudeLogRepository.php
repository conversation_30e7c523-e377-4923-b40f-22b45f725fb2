<?php

namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet;

use Domain\MinisterioSaude\Models\MinisterioSaudeLog;
use Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts\MinisterioSaudeLogRepositoryInterface;
use Domain\MinisterioSaude\Repositories\Common\BaseAbastractRepository;
use Illuminate\Database\Eloquent\Collection;

class MinisterioSaudeLogRepository extends BaseAbastractRepository implements MinisterioSaudeLogRepositoryInterface
{
    protected $model;

    public function __construct(MinisterioSaudeLog $model)
    {
        parent::__construct($model);
    }

    public function list(array $filtros): Collection
    {
        return $this->model->get();
    }

    // API 1.1 - Inserir Status Fatura
    public function storeInserirStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'inserir_status_fatura',
            '/status/criar',
            'POST',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 1.2 - Consultar Status
    public function storeConsultarStatus($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'consultar_status',
            '/status/consultar',
            'GET',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 1.3 - Consultar Endereço Local
    public function storeConsultaEndereco($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'consultar_endereco',
            '/endereco/consultar',
            'GET',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );

    }

    // API 1.4 - Consultar Itens
    public function storeConsultarItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'consultar_itens',
            '/itens/consultar',
            'POST',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 2.1 - Consultar Faturas
    public function storeConsultarFaturas($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'consultar_faturas',
            '/faturas/consultar',
            'GET',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 2.2 - Atualizar Status Fatura
    public function storeAtualizarStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'atualizar_status_fatura',
            '/faturas/status/atualizar',
            'PUT',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 3.1 - Consultar Pedidos Farmanet
    public function storeConsultarPedidosFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'consultar_pedidos_farmanet',
            '/pedidos-farmanet/consultar',
            'GET',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 3.2 - Atualizar Pedido Farmanet
    public function storeAtualizarPedidoFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'atualizar_pedido_farmanet',
            '/pedidos-farmanet/atualizar',
            'PUT',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 3.3 - Recebimento Faturas Farmanet
    public function storeReceberFaturasFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'receber_faturas_farmanet',
            '/faturas-farmanet/receber',
            'PUT',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    // API 3.4 - Obter Preço Médio de Itens
    public function storeObterPrecoMedioItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return $this->storeLog(
            'obter_preco_medio_itens',
            '/preco-medio-itens/obter',
            'GET',
            $requestData,
            $responseData,
            $sucesso,
            $erro
        );
    }

    public function storeLog($operacao, $endpoint, $metodo_http, $requestData, $responseData = null, $sucesso = false, $erro = null): Collection
    {
        return new Collection($this->model->create([
            'operacao' => $operacao,
            'endpoint' => $endpoint,
            'metodo_http' => $metodo_http,
            'request_data' => $requestData,
            'response_data' => $responseData,
            'status_code' => $responseData['status_code'] ?? 0,
            'request_token' => $responseData['RequestToken'] ?? null,
            'result_code' => $responseData['ResultCode'] ?? null,
            'message' => $responseData['Message'] ?? null,
            'dt_start' => $responseData['DtStart'] ?? null,
            'dt_end' => $responseData['DtEnd'] ?? null,
            'sucesso' => $sucesso,
            'erro_message' => $erro,
            'ip_address' => request()->ip()
        ]));
    }
}
