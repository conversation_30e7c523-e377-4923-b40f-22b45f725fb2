<?php

namespace Domain\MinisterioSaude\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EnderecoLocal extends Model
{
    use SoftDeletes;

    protected $connection = 'ministerio_saude_sp';
    protected $table = 'enderecos_locais';

    protected $fillable = [
        'id_gestor',
        'id_local',
        'codigo_endereco',
        'nome_local',
        'cep',
        'nome_tipo_logradouro',
        'nome_logradouro',
        'numero',
        'complemento',
        'nome_bairro',
        'nome_municipio',
        'uf',
        'latitude',
        'longitude',
        'distancia_km',
        'flag_registro',
        'dt_ultima_sincronizacao'
    ];

    protected $casts = [
        'flag_registro' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'distancia_km' => 'decimal:2',
        'dt_ultima_sincronizacao' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $dates = [
        'dt_ultima_sincronizacao',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('flag_registro', true);
    }

    public function scopePorGestor($query, $idGestor)
    {
        return $query->where('id_gestor', $idGestor);
    }

    public function scopePorLocal($query, $idLocal)
    {
        return $query->where('id_local', $idLocal);
    }

    public function scopeComGeolocalizacao($query)
    {
        return $query->whereNotNull('latitude')
                    ->whereNotNull('longitude');
    }

    public function scopeSemGeolocalizacao($query)
    {
        return $query->where(function($q) {
            $q->whereNull('latitude')
              ->orWhereNull('longitude');
        });
    }

    // Accessors
    public function getEnderecoCompletoAttribute()
    {
        $endereco = $this->nome_tipo_logradouro . ' ' . $this->nome_logradouro;
        
        if ($this->numero) {
            $endereco .= ', ' . $this->numero;
        }
        
        if ($this->complemento) {
            $endereco .= ', ' . $this->complemento;
        }
        
        $endereco .= ', ' . $this->nome_bairro;
        $endereco .= ', ' . $this->nome_municipio . ' - ' . $this->uf;
        $endereco .= ', CEP: ' . $this->getCepFormatadoAttribute();
        
        return $endereco;
    }

    public function getCepFormatadoAttribute()
    {
        return substr($this->cep, 0, 5) . '-' . substr($this->cep, 5, 3);
    }

    public function getTemGeolocalizacaoAttribute()
    {
        return !is_null($this->latitude) && !is_null($this->longitude);
    }

    // Mutators
    public function setCepAttribute($value)
    {
        // Remove formatação do CEP
        $this->attributes['cep'] = preg_replace('/\D/', '', $value);
    }

    public function setUfAttribute($value)
    {
        $this->attributes['uf'] = strtoupper($value);
    }

    // Métodos de negócio
    public function calcularDistancia()
    {
        if (!$this->tem_geolocalizacao) {
            return null;
        }

        // Coordenadas de "Rua Manoel Borba Gato, 100" (endereço base)
        $latitudeBase = -23.5889; // Coordenada exemplo, ajustar conforme necessário
        $longitudeBase = -46.6418; // Coordenada exemplo, ajustar conforme necessário

        return $this->calcularDistanciaHaversine(
            $latitudeBase,
            $longitudeBase,
            $this->latitude,
            $this->longitude
        );
    }

    private function calcularDistanciaHaversine($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // Raio da Terra em quilômetros

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return round($earthRadius * $c, 2);
    }

    public function atualizarDistancia()
    {
        if ($distancia = $this->calcularDistancia()) {
            $this->distancia_km = $distancia;
            $this->save();
        }
    }

    public function marcarComoSincronizado()
    {
        $this->dt_ultima_sincronizacao = now();
        $this->save();
    }
}
