<?php

namespace Domain\MinisterioSaude\Repositories\FaturaFarmanet\Contracts;

use Domain\MinisterioSaude\Repositories\Common\BaseRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

interface MinisterioSaudeLogRepositoryInterface extends BaseRepositoryInterface
{
    public function storeInserirStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeConsultarStatus($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeConsultaEndereco($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeConsultarItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeConsultarFaturas($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeAtualizarStatusFatura($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeConsultarPedidosFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeAtualizarPedidoFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeReceberFaturasFarmanet($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
    public function storeObterPrecoMedioItens($requestData, $responseData = null, $sucesso = false, $erro = null): Collection;
}
