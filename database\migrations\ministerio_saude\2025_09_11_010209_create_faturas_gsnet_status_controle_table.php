<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasGsnetStatusControleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('faturas_gsnet_status_controle', function (Blueprint $table) {
            $table->id();
            
            // Relacionamento com a fatura
            $table->foreignId('fatura_gsnet_id')->constrained('faturas_gsnet')->onDelete('cascade');
            
            // Controle de status
            $table->tinyInteger('status_anterior')->comment('Status anterior da fatura');
            $table->tinyInteger('status_atual')->comment('Status atual da fatura');
            $table->datetime('data_mudanca')->comment('Data/hora da mudança de status');
            
            // Informações da mudança
            $table->string('usuario_responsavel', 100)->nullable()->comment('Usuário responsável pela mudança');
            $table->string('origem_mudanca', 50)->comment('Origem da mudança (API, Sistema, Manual, etc)');
            $table->text('motivo_mudanca')->nullable()->comment('Motivo da mudança de status');
            
            // Dados da comunicação com API (se aplicável)
            $table->string('endpoint_api', 255)->nullable()->comment('Endpoint da API utilizado');
            $table->integer('status_code_api')->nullable()->comment('Status code retornado pela API');
            $table->text('resposta_api')->nullable()->comment('Resposta da API');
            $table->boolean('sucesso_api')->default(true)->comment('Se a comunicação com API foi bem-sucedida');
            
            // Informações de auditoria
            $table->string('ip_origem', 45)->nullable()->comment('IP de origem da operação');
            $table->text('observacoes')->nullable()->comment('Observações adicionais');
            
            // Dados originais para auditoria
            $table->longText('dados_originais')->nullable()->comment('Dados originais da mudança');
            
            // Timestamps padrão Laravel
            $table->timestamps();
            
            // Índices
            $table->index(['fatura_gsnet_id', 'data_mudanca']);
            $table->index(['status_atual', 'data_mudanca']);
            $table->index(['origem_mudanca', 'sucesso_api']);
            $table->index('data_mudanca');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('faturas_gsnet_status_controle');
    }
}
