<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrecoMedioItensTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ministerio_saude_sp')->create('preco_medio_itens', function (Blueprint $table) {
            $table->id();
            
            // Identificadores do programa e período
            $table->integer('codigo_programa')->comment('Código do programa (5,24,25,28)');
            $table->integer('ano_referencia')->comment('Ano de referência');
            $table->integer('mes_referencia')->nullable()->comment('Mês de referência');
            $table->string('estado_origem', 2)->nullable()->comment('Estado de origem (UF)');
            
            // Identificação do medicamento/fármaco
            $table->string('codigo_medicamento', 20)->nullable()->comment('Código do medicamento');
            $table->string('nome_medicamento', 255)->nullable()->comment('Nome do medicamento');
            $table->string('codigo_farmaco', 20)->nullable()->comment('Código do fármaco');
            $table->string('nome_farmaco', 255)->nullable()->comment('Nome do fármaco');
            $table->string('unidade_medida', 50)->nullable()->comment('Unidade de medida');
            
            // Dados de preço
            $table->decimal('preco_medio_unitario', 10, 4)->nullable()->comment('Preço médio unitário');
            $table->decimal('preco_minimo', 10, 4)->nullable()->comment('Preço mínimo encontrado');
            $table->decimal('preco_maximo', 10, 4)->nullable()->comment('Preço máximo encontrado');
            $table->integer('quantidade_amostras')->nullable()->comment('Quantidade de amostras utilizadas no cálculo');
            $table->decimal('desvio_padrao', 10, 4)->nullable()->comment('Desvio padrão dos preços');
            
            // Controle de dados
            $table->datetime('data_calculo')->nullable()->comment('Data do cálculo do preço médio');
            $table->datetime('data_ultima_atualizacao')->nullable()->comment('Última atualização');
            $table->text('observacoes')->nullable()->comment('Observações gerais');
            $table->longText('dados_originais')->nullable()->comment('JSON com dados originais da API');
            
            // Controle interno
            $table->timestamps();
            
            // Índices
            $table->index(['codigo_programa', 'ano_referencia', 'mes_referencia']);
            $table->index(['codigo_medicamento', 'codigo_farmaco']);
            $table->index(['estado_origem', 'codigo_programa']);
            $table->index('preco_medio_unitario');
            
            // Índice único para evitar duplicatas
            $table->unique([
                'codigo_programa', 
                'codigo_medicamento', 
                'codigo_farmaco', 
                'ano_referencia', 
                'mes_referencia', 
                'estado_origem'
            ], 'unique_preco_medio');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ministerio_saude_sp')->dropIfExists('preco_medio_itens');
    }
}
