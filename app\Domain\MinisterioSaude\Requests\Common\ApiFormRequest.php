<?php

namespace Domain\MinisterioSaude\Requests\Common;

use Domain\MinisterioSaude\Helpers\ServiceResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class ApiFormRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new HttpResponseException(
            ServiceResponse::validationError($validator->errors()->toArray())->toErrorResponse()
        );
    }
}
