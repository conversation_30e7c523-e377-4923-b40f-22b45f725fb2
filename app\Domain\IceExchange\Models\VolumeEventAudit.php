<?php

namespace Domain\IceExchange\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VolumeEventAudit extends Model
{
    protected $connection = null; // usar conex<PERSON> padrão
    protected $table = 'ice_change_volume_events_audit';

    protected $fillable = [
        'volume_event_id',
        'event_type',
        'event_date',
        'due_date',
        'user_id',
        'ice_type_id',
        'warehouse_id',
        'deadline_hours',
        'emit_label',
        'notfis_volume_id',
        'user_id_audit',
        'operation_type',
        'reason',
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'due_date' => 'datetime',
        'deadline_hours' => 'integer',
        'emit_label' => 'boolean',
    ];

    public function volumeEvent(): BelongsTo
    {
        return $this->belongsTo(VolumeEvent::class, 'volume_event_id');
    }

    public function notfisVolume(): BelongsTo
    {
        return $this->belongsTo(\Domain\NOTFIS\Models\NotfisVolume::class, 'notfis_volume_id');
    }

    public function user(): BelongsTo
    {
        return $this->setConnection('users_service')->belongsTo(\App\Models\User::class);
    }

    public function userAudit(): BelongsTo
    {
        return $this->setConnection('users_service')->belongsTo(\App\Models\User::class, 'user_id_audit');
    }

    public function iceType(): BelongsTo
    {
        return $this->belongsTo(IceItem::class, 'ice_type_id');
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(IceWarehouse::class, 'warehouse_id');
    }
} 