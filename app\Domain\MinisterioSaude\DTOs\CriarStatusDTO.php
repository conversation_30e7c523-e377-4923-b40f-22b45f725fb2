<?php

namespace Domain\MinisterioSaude\DTOs;

class CriarStatusDTO
{
    public string $idOrigem;
    public string $nomeStatus;
    public string $descricaoStatus;
    public string $accessToken;
    public string $systemCode;

    public function __construct(
        string $idOrigem,
        string $nomeStatus,
        string $descricaoStatus,
        string $accessToken,
        string $systemCode
    ) {
        $this->idOrigem = $idOrigem;
        $this->nomeStatus = $nomeStatus;
        $this->descricaoStatus = $descricaoStatus;
        $this->accessToken = $accessToken;
        $this->systemCode = $systemCode;
    }

    public function toArray(): array
    {
        return [
            'Data' => [
                'IdOrigem' => $this->idOrigem,
                'NomeStatus' => $this->nomeStatus,
                'DescricaoStatus' => $this->descricaoStatus
            ],
            'AccessToken' => $this->accessToken,
            'SystemCode' => $this->systemCode
        ];
    }

    public function validate(): array
    {
        $errors = [];

        if (empty($this->idOrigem)) {
            $errors[] = 'IdOrigem não informado';
        } elseif (strlen($this->idOrigem) > 11) {
            $errors[] = 'IdOrigem deve ter no máximo 11 caracteres';
        }

        if (empty($this->nomeStatus)) {
            $errors[] = 'NomeStatus não informado';
        } elseif (strlen($this->nomeStatus) > 40) {
            $errors[] = 'NomeStatus deve ter no máximo 40 caracteres';
        }

        if (empty($this->descricaoStatus)) {
            $errors[] = 'DescricaoStatus não informado';
        } elseif (strlen($this->descricaoStatus) > 240) {
            $errors[] = 'DescricaoStatus deve ter no máximo 240 caracteres';
        }

        if (empty($this->accessToken)) {
            $errors[] = 'AccessToken não informado';
        } elseif (strlen($this->accessToken) > 240) {
            $errors[] = 'AccessToken deve ter no máximo 240 caracteres';
        }

        if (empty($this->systemCode)) {
            $errors[] = 'SystemCode não informado';
        }

        return $errors;
    }

    public function isValid(): bool
    {
        return empty($this->validate());
    }
}
