<?php

namespace Domain\MinisterioSaude\Services\FaturaFarmanet\StatusOperador\Input;

use Illuminate\Http\Request;

class ConsultarStatusInput
{
    public ?string $idOrigem;
    public ?string $nomeStatus;
    public ?string $idStatus;

    public function __construct(?string $idOrigem, ?string $idStatus, ?string $nomeStatus)
    {
        $this->idOrigem = $idOrigem;
        $this->idStatus  = $idStatus;
        $this->nomeStatus = $nomeStatus;
    }

    public function toArray(): array
    {
        return [
            'id_origem' => $this->idOrigem,
            'id_status' => $this->idStatus,
            'nome_status' => $this->nomeStatus,
        ];
    }

    public function toQueryParams(string $accessToken): array
    {
        $params = [
            'AccessToken' => $accessToken
        ];

        if ($this->idOrigem) {
            $params['IdOrigem'] = $this->idOrigem;
        }

        if ($this->idStatus) {
            $params['IdStatus'] = $this->idStatus;
        }

        if ($this->nomeStatus) {
            $params['NomeStatus'] = $this->nomeStatus;
        }

        return $params;
    }

    public static function fromRequest(Request $request): ConsultarStatusInput
    {
        return new self(
            $request->input('id_origem') ?? null,
            $request->input('id_status') ?? null,
            $request->input('nome_status') ?? null,
        );
    }

    public static function fromArray(array $data): ConsultarStatusInput
    {
        return new self(
            $data['id_origem'] ?? null,
            $data['id_status'] ?? null,
            $data['nome_status'] ?? null,
        );
    }
}
