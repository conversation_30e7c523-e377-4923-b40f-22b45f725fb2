<?php

namespace Domain\MinisterioSaude\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StatusOperadorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray($request): array
    {
        return [
            'IdStatus' => $this->id,
            'IdOrigem' => $this->id_origem,
            'NomeStatus' => $this->nome_status,
            'DescricaoStatus' => $this->descricao_status,
            'FlagResgistro' => $this->flag_registro ? '1' : '0',
            'DtInclusao' => $this->created_at?->format('Y-m-d\TH:i:s'),
            'DtAlteracao' => $this->updated_at?->format('Y-m-d\TH:i:s')
        ];
    }
}
