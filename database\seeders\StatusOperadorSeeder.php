<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Domain\MinisterioSaude\Models\StatusOperador;

class StatusOperadorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $statusList = [
            [
                'id_origem' => '1001',
                'nome_status' => 'Recebido',
                'descricao_status' => 'Pedido recebido e está sendo processado',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1002',
                'nome_status' => 'Em Processamento',
                'descricao_status' => 'Pedido em processo de separação e preparação',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1003',
                'nome_status' => 'Separação',
                'descricao_status' => 'Processo de separação dos produtos para distribuição',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1004',
                'nome_status' => 'Em Trânsito',
                'descricao_status' => 'Produto em trânsito para entrega ao destino',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1005',
                'nome_status' => 'Entregue',
                'descricao_status' => 'Produto entregue ao responsável no destino',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1006',
                'nome_status' => 'Recusado',
                'descricao_status' => 'Entrega recusada pelo destinatário',
                'flag_registro' => true
            ],
            [
                'id_origem' => '1007',
                'nome_status' => 'Cancelado',
                'descricao_status' => 'Pedido cancelado a pedido do cliente',
                'flag_registro' => true
            ]
        ];

        foreach ($statusList as $status) {
            StatusOperador::updateOrCreate(
                ['id_origem' => $status['id_origem']],
                $status
            );
        }

        $this->command->info('Status operador criados com sucesso!');
    }
}
