<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ice_change_volume_events_audit', function (Blueprint $table) {
            $table->text('reason')->nullable()->after('operation_type')->collation('Latin1_General_CI_AS');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ice_change_volume_events_audit', function (Blueprint $table) {
            $table->dropColumn('reason');
        });
    }
};
