<?php

namespace Domain\MinisterioSaude\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class StatusOperadorCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray($request): array
    {
        return [
            'Data' => StatusOperadorResource::collection($this->collection),
            'TotalPages' => 1,
            'TotalRecords' => $this->collection->count(),
            'RequestToken' => (string) \Illuminate\Support\Str::uuid(),
            'ResultCode' => 0,
            'Message' => 'Sucesso',
            'DtStart' => now()->format('Y-m-d\TH:i:s.uP'),
            'DtStartFmt' => now()->format('d/m/Y h:i:s A'),
            'DtEnd' => now()->format('Y-m-d\TH:i:s.uP'),
            'DtEndFmt' => now()->format('d/m/Y h:i:s A'),
            'ValidationSummary' => null
        ];
    }
}
